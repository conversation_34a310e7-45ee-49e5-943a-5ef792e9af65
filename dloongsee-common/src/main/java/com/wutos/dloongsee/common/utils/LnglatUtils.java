package com.wutos.dloongsee.common.utils;

import com.wutos.dloongsee.common.dto.Lnglat;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 经纬度工具类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/15
 */
@UtilityClass
public class LnglatUtils {

    /**
     * 经纬度字符串转换
     *
     * @param lnglatStr lng,lat|lng,lat...
     * @return 经纬度
     */
    public static List<Lnglat> parse(String lnglatStr) {
        if (StringUtils.isEmpty(lnglatStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(lnglatStr.split("\\|"))
                .map(p -> p.split(","))
                .filter(arr -> arr.length == 2)
                .map(arr -> new Lnglat(Double.parseDouble(arr[0]), Double.parseDouble(arr[1])))
                .collect(Collectors.toList());
    }
}
