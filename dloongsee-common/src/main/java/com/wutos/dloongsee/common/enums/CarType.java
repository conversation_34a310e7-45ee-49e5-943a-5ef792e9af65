package com.wutos.dloongsee.common.enums;

import lombok.Getter;

@Getter
public enum CarType {
    SMALL(0),
    MIDDLE(1),
    BIG(2);

    private final int code;

    CarType(int code) {
        this.code = code;
    }

    public static CarType getByCode(int code) {
        for (CarType value : CarType.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
