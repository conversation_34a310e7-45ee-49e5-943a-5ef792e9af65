package com.wutos.dloongsee.common.components;

import lombok.Getter;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * influxdb工具类
 * <p>
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Getter
@Component
public class InfluxdbComponent {
    @Value("${spring.influx.database}")
    private String influxDatabase;

    @Autowired
    private InfluxDBMapper mapper;

    @Autowired
    private InfluxDB influxDB;

    /**
     * 查询
     */
    public <T> List<T> query(String sql, final Class<T> clazz) {
        return mapper.query(new Query(sql, influxDatabase), clazz);
    }

    /**
     * 原始查询
     */
    public List<QueryResult.Series> queryOrigin(String sql) {
        return influxDB.query(new Query(sql, influxDatabase)).getResults().get(0).getSeries();
    }

    public void insertByPoint(Point point) {
        influxDB.write(influxDatabase, "autogen",point);
    }

    public void insertByPoints(List<Point> points) {
        BatchPoints batchPoints = BatchPoints
                .database(influxDatabase)
                .build();
        points.forEach(batchPoints::point);
        influxDB.write(batchPoints);
    }

}
