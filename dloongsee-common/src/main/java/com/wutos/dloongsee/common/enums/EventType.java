package com.wutos.dloongsee.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件类型
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/16
 */
@Getter
@AllArgsConstructor
public enum EventType {
    /**
     * 逆行
     */
    BACK_DRIVE(EventCategory.DRIVE, "逆行"),
    /**
     * 占用应急
     */
    EMERGENCY_LANE(EventCategory.DRIVE, "长期占用应急车道"),
    /**
     * 超速
     */
    OVER_SPEED(EventCategory.DRIVE, "超速"),
    /**
     * 低速
     */
    LOW_SPEED(EventCategory.DRIVE, "低速"),
    /**
     * 停车
     */
    STOP(EventCategory.DRIVE, "停车"),

    /**
     * 行人
     */
    PEDESTRIAN(EventCategory.PASS, "行人入侵"),
    /**
     * 抛洒物
     */
    SPRINKLE(EventCategory.PASS, "抛洒物"),

    /**
     * 行车道占用
     */
    DRIVE_LANE_OCCUPY(EventCategory.PASS, "行车道占用"),

    CONGESTION_MAIN(EventCategory.TRAFFIC, "主线拥堵"),

    CONGESTION_SLAVE(EventCategory.TRAFFIC, " 支线拥堵"),

    SPEED_DIFF(EventCategory.TRAFFIC, "速度差异常"),

    TRAFFIC_SURGE(EventCategory.TRAFFIC, "流量激增");

    private final EventCategory category;

    private final String label;

    public static List<EventType> getAllDriveEvent() {
        return Arrays.stream(EventType.values())
                .filter(eventType -> eventType.getCategory() == EventCategory.DRIVE)
                .collect(Collectors.toList());
    }
}
