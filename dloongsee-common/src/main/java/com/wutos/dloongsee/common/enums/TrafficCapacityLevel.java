package com.wutos.dloongsee.common.enums;

import lombok.Getter;

/**
 * 道路通行能力等级
 */
@Getter
public enum TrafficCapacityLevel {
    A("自由流"),
    B("稳定流"),
    C("稳定流"),
    D("稳定流"),
    E("不稳定流"),
    F("强制流");

    private final String label;

    TrafficCapacityLevel(String label) {
        this.label = label;
    }

    //vc比获取通行能力等级
    public static TrafficCapacityLevel getLevel(double vc) {
        if (vc <= 0.35) {
            return TrafficCapacityLevel.A;
        } else if (vc <= 0.55) {
            return TrafficCapacityLevel.B;
        } else if (vc <= 0.75) {
            return TrafficCapacityLevel.C;
        } else if (vc <= 0.9) {
            return TrafficCapacityLevel.D;
        } else if (vc <= 1.0) {
            return TrafficCapacityLevel.E;
        } else {
            return TrafficCapacityLevel.F;
        }
    }
}
