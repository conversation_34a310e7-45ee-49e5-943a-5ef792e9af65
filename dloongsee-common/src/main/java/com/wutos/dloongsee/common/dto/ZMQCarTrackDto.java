package com.wutos.dloongsee.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wutos.dloongsee.common.enums.CarType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZMQCarTrackDto {
    /**
     * 车辆id
     */
    private String id;

    /**
     * 车牌号
     */
    private String cn;

    /**
     * 车辆类型
     */
    private int type;

    /**
     * 车速
     */
    private float speed;

    /**
     * 车道号
     */
    private int wn;

    /**
     * 里程
     */
    private int mil;

    /**
     * 方向
     */
    private int direction;

    /**
     * 报文时间戳
     */
    private long messageTicks;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String index;

    @Builder.Default
    private boolean getOut = false;

    @Builder.Default
    private boolean comeIn = false;

    @JsonIgnore
    public CarType getCarType() {
        return CarType.getByCode(this.type);
    }

    /**
     * 上下行 up down
     */
    @JsonIgnore
    public RoadDirection getRoadDirection() {
        String[] split = id.split("@");
        return RoadDirection.valueOf(split[split.length - 1].toUpperCase());
    }
}
