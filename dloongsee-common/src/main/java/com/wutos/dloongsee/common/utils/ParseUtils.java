package com.wutos.dloongsee.common.utils;

import lombok.experimental.UtilityClass;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@UtilityClass
public class ParseUtils {

    public static String parseHexStringToString(String hexString) {
        String[] hexValues = hexString.split(",");
        byte[] byteArray = new byte[hexValues.length];
        for (int i = 0; i < hexValues.length; i++) {
            byteArray[i] = (byte) Integer.parseInt(hexValues[i].trim().substring(2), 16);
        }
        return new String(byteArray, StandardCharsets.UTF_8);
    }

    public static int byteToInt(byte[] bytes, int from, int to) {
        return ByteBuffer.wrap(Arrays.copyOfRange(bytes, from, to)).order(ByteOrder.LITTLE_ENDIAN).getInt();
    }

    public static short byteToShort(byte[] bytes, int from, int to) {
        return ByteBuffer.wrap(Arrays.copyOfRange(bytes, from, to)).order(ByteOrder.LITTLE_ENDIAN).getShort();
    }

    public static long byteToLong(byte[] bytes, int from, int to) {
        return ByteBuffer.wrap(Arrays.copyOfRange(bytes, from, to)).order(ByteOrder.LITTLE_ENDIAN).getLong();
    }

    public static float byteToFloat(byte[] bytes, int from, int to) {
        return ByteBuffer.wrap(Arrays.copyOfRange(bytes, from, to)).order(ByteOrder.LITTLE_ENDIAN).getFloat();
    }
}
