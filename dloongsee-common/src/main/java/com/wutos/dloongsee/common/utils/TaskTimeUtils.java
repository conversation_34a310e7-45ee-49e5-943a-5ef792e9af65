package com.wutos.dloongsee.common.utils;

import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <p>
 * 任务时间工具类
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@UtilityClass
public class TaskTimeUtils {

    /**
     * 计算上一次执行时间
     *
     * @param currentTime     当前时间
     * @param intervalSeconds 间隔秒数
     * @return 上一次执行时间
     */
    public static LocalDateTime getLastExecutionTime(LocalDateTime currentTime, int intervalSeconds) {
        // 为了避免纳秒影响计算，将时间截断到秒
        LocalDateTime truncatedTime = currentTime.truncatedTo(ChronoUnit.SECONDS);
        long secondsOfDay = truncatedTime.toLocalTime().toSecondOfDay();
        long secondsSinceLastExecution = secondsOfDay % intervalSeconds;
        return truncatedTime.minusSeconds(secondsSinceLastExecution);
    }

    /**
     * 计算下一次执行时间
     *
     * @param currentTime     当前时间
     * @param intervalSeconds 间隔秒数
     * @return 下一次执行时间
     */
    public static LocalDateTime getNextExecutionTime(LocalDateTime currentTime, int intervalSeconds) {
        LocalDateTime lastExecutionTime = getLastExecutionTime(currentTime, intervalSeconds);
        return lastExecutionTime.plusSeconds(intervalSeconds);
    }
}
