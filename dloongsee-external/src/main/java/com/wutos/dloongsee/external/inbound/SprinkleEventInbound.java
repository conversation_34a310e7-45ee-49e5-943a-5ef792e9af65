package com.wutos.dloongsee.external.inbound;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SprinkleEventInbound {

    /**
     * 事件id
     */
    private String id;

    /**
     * 事发车道
     */
    private Integer lane;

    /**
     * 事发里程
     */
    private int[] pos;

    /**
     * 车道方向 小里程->大里程为1，大里程->小里程为2
     */
    private Integer direction;

    /**
     * 时间
     */
    private String time;

}
