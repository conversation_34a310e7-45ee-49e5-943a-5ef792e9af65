package com.wutos.dloongsee.external;

import com.wutos.dloongsee.common.utils.ParseUtils;
import org.zeromq.SocketType;
import org.zeromq.ZContext;
import org.zeromq.ZMQ;

public class ZMQUtils {

    public static ZMQ.Socket connect(String url, String topic) {
        ZContext zContext = new ZContext();
        ZMQ.Socket socket = zContext.createSocket(SocketType.SUB);
        // 开启TCP保活机制，防止网络连接因长时间无数据而中断
        socket.setTCPKeepAlive(1);
        // 设置发送ZMTP心跳的时间间隔, 单位:ms
        socket.setHeartbeatIvl(5 * 60 * 1000);
        // 设置ZMTP心跳的超时时间, 单位:ms
        socket.setHeartbeatTimeout(60 * 1000);
        // 设置ZMTP心跳的TTL值, 单位:ms
        socket.setHeartbeatTtl(10 * 60 * 1000);
        socket.connect(url);
        socket.subscribe(topic != null ? ParseUtils.parseHexStringToString(topic) : "");
        return socket;
    }
}
