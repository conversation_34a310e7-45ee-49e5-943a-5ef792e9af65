package com.wutos.dloongsee.external.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Measurement(name = "tb_car_track")
public class ZMQCarTrack {
    @Column(name = "time")
    private String time;
    @Column(name = "CarLicense")
    private String carLicense;
    @Column(name = "CarLine")
    private Integer carLine;
    @Column(name = "CarSpeed")
    private Double carSpeed;
    @Column(name = "CarType")
    private Integer carType;
    @Column(name = "Direction", tag = true)
    private Integer direction;
    @Column(name = "Id")
    private String id;
    @Column(name = "Index", tag = true)
    private String index;
    @Column(name = "MessageTicks")
    private Long messageTicks;
    @Column(name = "Mil")
    private Integer mil;

    /**
     * 上下行 up down
     */
    @JsonIgnore
    public String getRoadOriginalDirection() {
        String[] split = id.split("@");
        return split[split.length - 1];
    }
}
