package com.wutos.dloongsee.external.convertor;


import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.external.entity.ZMQCarTrack;
import com.wutos.dloongsee.external.inbound.CarTrackInbound;
import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;

@UtilityClass
public class CarTrackConvertor {

    public ZMQCarTrackDto inboundToOutbound(CarTrackInbound.CarInfoDto carInfoDto, long timestamp, RoadDirection direction) {

        String carId = carInfoDto.getCarId() + "@" + direction;
        if (carInfoDto.getWayNo() == 99) {
            carInfoDto.setWayNo(-2);
        }
        if (carInfoDto.getWayNo() == 100) {
            carInfoDto.setWayNo(-1);
        }
        //过滤掉速度超过300的脏数据
        if (carInfoDto.getSpeed() > 85){
            return null;
        }
        return ZMQCarTrackDto.builder()
                .id(carId)
                .cn(carInfoDto.getCarNumber())
                .wn(carInfoDto.getWayNo())
                .mil(carInfoDto.getMileage())
                .speed(carInfoDto.getSpeed())
                .direction(carInfoDto.getDirect())
                .type(carInfoDto.getCarType())
                .updateTime(LocalDateTime.now())
                .messageTicks(timestamp)
                .getOut(carInfoDto.getWayNo() == -2)
                .comeIn(carInfoDto.getWayNo() == -1)
                .build();
    }

    public ZMQCarTrack outBoundToTb(ZMQCarTrackDto zmqCarTrackDto){
        return ZMQCarTrack.builder()
                .id(zmqCarTrackDto.getId())
                .carLicense(zmqCarTrackDto.getCn())
                .carLine(zmqCarTrackDto.getWn())
                .carType(zmqCarTrackDto.getType())
                .carSpeed(Double.valueOf(Float.valueOf(zmqCarTrackDto.getSpeed()).toString()))
                .direction(zmqCarTrackDto.getDirection())
                .messageTicks(zmqCarTrackDto.getMessageTicks())
                .mil(zmqCarTrackDto.getMil())
                .build();
    }
}
