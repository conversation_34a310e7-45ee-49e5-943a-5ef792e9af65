package com.wutos.dloongsee.external.properties;

import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "zmq.event")
public class ZMQEventProperties {

    private List<EventAlgorithm> algorithms;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EventAlgorithm {
        private String name;
        private String desc;
        private List<EventType> types;
        private boolean enabled;
        private List<ZMQEventServer> servers;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ZMQEventServer {
            private String url;
            private RoadDirection direction;
            private String topic;
        }
    }
}
