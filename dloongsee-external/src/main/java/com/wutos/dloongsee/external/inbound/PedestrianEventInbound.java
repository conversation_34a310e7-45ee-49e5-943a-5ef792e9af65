package com.wutos.dloongsee.external.inbound;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedestrianEventInbound {

    /**
     * 事件id
     */
    private String id;

    /**
     * 事件里程
     */
    private Integer mileage;

    /**
     * 事件状态 0：开始，1：持续，2：结束
     */
    private Integer status;

    /**
     * 车道方向 小里程->大里程为1，大里程->小里程为2
     */
    private Integer direction;

    /**
     * 时间
     */
    private String time;

}
