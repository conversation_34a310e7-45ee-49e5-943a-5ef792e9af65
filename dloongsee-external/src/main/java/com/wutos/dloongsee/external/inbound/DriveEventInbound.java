package com.wutos.dloongsee.external.inbound;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DriveEventInbound {

    private List<DriveEventInfoInbound> driveEventInfoInboundList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DriveEventInfoInbound {
        /**
         * 车辆id
         */
        private String id;

        /**
         * 逆行
         */
        private Boolean backDrive;

        /**
         * 应急车道占用
         */
        private Boolean emergencyLane;

        /**
         * 超速行驶
         */
        private Boolean overSpeed;

        /**
         * 低速行驶
         */
        private Boolean lowSpeed;

        /**
         * 违法停车
         */
        private Boolean stop;

        /**
         * 违规变道
         */
        private Boolean changeLane;

        /**
         * 障碍物
         */
        private Boolean obstacle;

        /**
         * 减速带
         */
        private Boolean block;

        /**
         * 行人
         */
        private Boolean pedestrian;

        /**
         * 交通事故
         */
        private Boolean accident;

        /**
         * 抛洒物
         */
        private Boolean sprinkle;

        /**
         * 车牌
         */
        private String plate;

        /**
         * 车型
         */
        private int type;

        /**
         * 当前瞬时速度
         */
        private double speed;

        /**
         * 车道
         */
        private int lane;

        /**
         * 车辆里程
         */
        private int pos;

        /**
         * 车辆方向
         */
        private int direction;

        /**
         * 时间
         */
        private String time;
    }
}
