package com.wutos.dloongsee.external;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.common.components.InfluxdbComponent;
import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.springevent.ZMQEventSpringEvent;
import com.wutos.dloongsee.external.convertor.EventConvertor;
import com.wutos.dloongsee.external.inbound.DriveEventInbound;
import com.wutos.dloongsee.external.inbound.PedestrianEventInbound;
import com.wutos.dloongsee.external.inbound.SprinkleEventInbound;
import com.wutos.dloongsee.external.properties.ZMQEventProperties;
import com.wutos.dloongsee.external.properties.ZMQEventProperties.EventAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.Point;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.zeromq.ZMQ;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@EnableConfigurationProperties(ZMQEventProperties.class)
public class ZMQEventProcessor {

    private final ZMQEventProperties zmqEventProperties;
    private final InfluxdbComponent influxdbComponent;
    private final ThreadPoolTaskExecutor zmqDataPersistenceThreadPoolTaskExecutor;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ObjectMapper objectMapper;

    public ZMQEventProcessor(ZMQEventProperties zmqEventProperties, InfluxdbComponent influxdbComponent, ThreadPoolTaskExecutor zmqDataPersistenceThreadPoolTaskExecutor,
                             ApplicationEventPublisher applicationEventPublisher, ObjectMapper objectMapper) {
        this.zmqEventProperties = zmqEventProperties;
        this.influxdbComponent = influxdbComponent;
        this.zmqDataPersistenceThreadPoolTaskExecutor = zmqDataPersistenceThreadPoolTaskExecutor;
        this.applicationEventPublisher = applicationEventPublisher;
        this.objectMapper = objectMapper;
    }

    //根据配置文件启动对应算法
    @PostConstruct
    private void processZMQEvent() {
        zmqEventProperties.getAlgorithms().stream()
                .filter(EventAlgorithm::isEnabled).collect(Collectors.toList())
                .forEach(eventAlgorithm -> {
                    for (EventAlgorithm.ZMQEventServer zmqServer : eventAlgorithm.getServers()) {
                        ZMQ.Socket socket = ZMQUtils.connect(zmqServer.getUrl(), zmqServer.getTopic());
                        if (eventAlgorithm.getTypes().contains(EventType.PEDESTRIAN)) {
                            processPedestrianEvent(socket, zmqServer.getDirection());
                            continue;
                        }
                        if (eventAlgorithm.getTypes().contains(EventType.SPRINKLE)) {
                            processSprinkleEvent(socket, zmqServer.getDirection());
                            continue;
                        }
                        processDriveEvent(eventAlgorithm, socket, zmqServer.getDirection());
                    }
                });
    }

    private void processDriveEvent(EventAlgorithm eventAlgorithm, ZMQ.Socket socket, RoadDirection direction) {
        new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                byte[] bytes = socket.recv();
                if (bytes != null && bytes.length > 0) {
                    String message = new String(bytes, StandardCharsets.UTF_8);
                    log.debug("ZMQ接收到event: {}", message);
                    zmqDataPersistenceThreadPoolTaskExecutor.execute(() -> persistEventMetaData(message, direction));
                    String eventStr = message.substring(1).replaceAll("[\\x00-\\x1F]", "");
                    try {
                        List<DriveEventInbound.DriveEventInfoInbound> driveEventInfoInboundList = objectMapper.readValue(eventStr, new TypeReference<List<DriveEventInbound.DriveEventInfoInbound>>() {
                        });
                        driveEventInfoInboundList.forEach(driveEventInfoInbound -> {
                            ZMQEventDTO zmqEventDTO = EventConvertor.driveEventInboundToOutBound(driveEventInfoInbound, eventAlgorithm.getTypes(), direction);
                            if (zmqEventDTO != null) {
                                publishEvent(zmqEventDTO);
                            }
                        });
                    } catch (JsonProcessingException e) {
                        log.error("处理驾驶类事件失败: {}", eventStr, e);
                    }
                }
            }
        }, "drive-event-process-" + direction).start();
    }

    private void processPedestrianEvent(ZMQ.Socket socket, RoadDirection direction) {
        new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                byte[] bytes = socket.recv();
                if (bytes != null && bytes.length > 0) {
                    String message = new String(bytes, StandardCharsets.UTF_8);
                    log.debug("ZMQ接收到event: {}", message);
                    zmqDataPersistenceThreadPoolTaskExecutor.execute(() -> persistEventMetaData(message, direction));
                    String eventStr = message.substring(1).replaceAll("[\\x00-\\x1F]", "");
                    try {
                        List<PedestrianEventInbound> pedestrianEventInbounds = objectMapper.readValue(eventStr, new TypeReference<List<PedestrianEventInbound>>() {
                        });
                        pedestrianEventInbounds.forEach(pedestrianEventInbound -> {
                            ZMQEventDTO zmqEventDTO = EventConvertor.pedestrianEventInboundToOutBound(pedestrianEventInbound, direction);
                            if (zmqEventDTO != null) {
                                publishEvent(zmqEventDTO);
                            }
                        });
                    } catch (JsonProcessingException e) {
                        log.error("处理行人入侵事件失败: {}", eventStr, e);
                    }
                }
            }
        }, "pedestrian-event-process-" + direction).start();
    }

    private void processSprinkleEvent(ZMQ.Socket socket, RoadDirection direction) {
        new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                byte[] bytes = socket.recv();
                if (bytes != null && bytes.length > 0) {
                    String message = new String(bytes, StandardCharsets.UTF_8);
                    log.debug("ZMQ接收到event: {}", message);
                    zmqDataPersistenceThreadPoolTaskExecutor.execute(() -> persistEventMetaData(message, direction));
                    String eventStr = message.substring(1).replaceAll("[\\x00-\\x1F]", "");
                    try {
                        SprinkleEventInbound sprinkleEventInbound = objectMapper.readValue(eventStr, new TypeReference<SprinkleEventInbound>() {
                        });
                        ZMQEventDTO zmqEventDTO = EventConvertor.sprinkleEventInboundToOutBound(sprinkleEventInbound, direction);
                        publishEvent(zmqEventDTO);
                    } catch (JsonProcessingException e) {
                        log.error("处理抛洒物事件失败: {}", eventStr, e);
                    }
                }
            }
        }, "sprinkle-event-process-" + direction).start();
    }

    private void persistEventMetaData(String message, RoadDirection direction) {
        Point point = Point.measurement("tb_event_metadata")
                .tag("direction", direction.name())
                .addField("data", message)
                .build();
        influxdbComponent.insertByPoint(point);
    }

    private void publishEvent(ZMQEventDTO zmqEventDTO) {
        applicationEventPublisher.publishEvent(new ZMQEventSpringEvent(zmqEventDTO));
    }
}
