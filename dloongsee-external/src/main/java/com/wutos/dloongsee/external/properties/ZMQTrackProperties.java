package com.wutos.dloongsee.external.properties;

import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "zmq.track")
public class ZMQTrackProperties {

    private List<ZMQTrackServer> servers;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ZMQTrackServer {
        private String url;
        private RoadDirection direction;
        private String topic;
    }
}
