package com.wutos.dloongsee.external.convertor;

import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.external.inbound.DriveEventInbound;
import com.wutos.dloongsee.external.inbound.PedestrianEventInbound;
import com.wutos.dloongsee.external.inbound.SprinkleEventInbound;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@UtilityClass
public class EventConvertor {

    private LocalDateTime parseEventTime(String eventTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        String timeStr = eventTime.replaceFirst(":(\\d{3})$", ".$1");
        return LocalDateTime.parse(timeStr, formatter);
    }

    public ZMQEventDTO driveEventInboundToOutBound(DriveEventInbound.DriveEventInfoInbound driveEventInfoInbound, List<EventType> types, RoadDirection direction) {
        EventType eventType = null;
        boolean isStarted = true;
        if (driveEventInfoInbound.getBackDrive() != null) {
            eventType = EventType.BACK_DRIVE;
            isStarted = driveEventInfoInbound.getBackDrive();
        }
        if (driveEventInfoInbound.getEmergencyLane() != null) {
            eventType = EventType.EMERGENCY_LANE;
            isStarted = driveEventInfoInbound.getEmergencyLane();
        }
        if (driveEventInfoInbound.getOverSpeed() != null) {
            eventType = EventType.OVER_SPEED;
            isStarted = driveEventInfoInbound.getOverSpeed();
        }
        if (driveEventInfoInbound.getStop() != null) {
            eventType = EventType.STOP;
            isStarted = driveEventInfoInbound.getStop();
        }
        if (driveEventInfoInbound.getLowSpeed() != null) {
            log.info("低速事件不处理-------");
        }
        if (driveEventInfoInbound.getLane() == 4) {
            driveEventInfoInbound.setLane(-2);
        }
        if (driveEventInfoInbound.getLane() == 5) {
            driveEventInfoInbound.setLane(-1);
        }
        // 低速或不在当前算法管理的类型内, 不处理
        if (eventType == null || !types.contains(eventType)) return null;
        String carId = null;
        String carNum = null;
        String eventId;
        if (!StringUtils.isEmpty(driveEventInfoInbound.getId())) {
            carId = driveEventInfoInbound.getId() + "@" + direction;
            carNum = StringUtils.isEmpty(driveEventInfoInbound.getPlate()) ? "未识别车牌" : driveEventInfoInbound.getPlate();
            eventId = driveEventInfoInbound.getId() + eventType + "@" + direction;
        } else {
            eventId = eventType.name() + driveEventInfoInbound.getLane() + driveEventInfoInbound.getPos() + "@" + direction;
        }
        ZMQEventDTO.ZMQEventDTOBuilder builder = ZMQEventDTO.builder()
                .eventId(eventId)
                .carId(carId)
                .carNum(carNum)
                .direction(driveEventInfoInbound.getDirection())
                .speed(driveEventInfoInbound.getSpeed())
                .type(eventType)
                .lane(driveEventInfoInbound.getLane())
                .updateTime(LocalDateTime.now());
        if (isStarted) {
            return builder
                    .startTime(parseEventTime(driveEventInfoInbound.getTime()))
                    .startMil(driveEventInfoInbound.getPos())
                    .isStarted(true)
                    .build();
        } else {
            return builder
                    .endTime(parseEventTime(driveEventInfoInbound.getTime()))
                    .endMil(driveEventInfoInbound.getPos())
                    .isStarted(false)
                    .build();
        }
    }

    public ZMQEventDTO pedestrianEventInboundToOutBound(PedestrianEventInbound pedestrianEventInbound, RoadDirection direction) {
        String eventId = EventType.PEDESTRIAN + pedestrianEventInbound.getId() + "@" + direction;
        ZMQEventDTO.ZMQEventDTOBuilder builder = ZMQEventDTO.builder()
                .eventId(eventId)
                .type(EventType.PEDESTRIAN)
                .direction(pedestrianEventInbound.getDirection())
                .updateTime(LocalDateTime.now())
                .lane(9);
        if (pedestrianEventInbound.getStatus() == 0) {
            return builder
                    .startMil(pedestrianEventInbound.getMileage())
                    .startTime(parseEventTime(pedestrianEventInbound.getTime()))
                    .isStarted(true)
                    .build();
        } else if (pedestrianEventInbound.getStatus() == 2) {
            return builder
                    .endMil(pedestrianEventInbound.getMileage())
                    .endTime(parseEventTime(pedestrianEventInbound.getTime()))
                    .isStarted(true)
                    .build();
        } else {
            return null;
        }
    }

    public ZMQEventDTO sprinkleEventInboundToOutBound(SprinkleEventInbound sprinkleEventInbound, RoadDirection direction) {
        String eventId = EventType.SPRINKLE + sprinkleEventInbound.getId() + "@" + direction;
        return ZMQEventDTO.builder()
                .eventId(eventId)
                .startTime(parseEventTime(sprinkleEventInbound.getTime()))
                .startMil(sprinkleEventInbound.getPos()[0])
                .endMil(sprinkleEventInbound.getPos()[1])
                .type(EventType.SPRINKLE)
                .lane(sprinkleEventInbound.getLane())
                .direction(sprinkleEventInbound.getDirection())
                .updateTime(LocalDateTime.now())
                .isStarted(true)
                .build();
    }
}
