<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wutos.dloongsee</groupId>
        <artifactId>dloongsee</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>dloongsee-api</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wutos.dloongsee</groupId>
            <artifactId>dloongsee-common</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.wutos.dloongsee</groupId>
            <artifactId>dloongsee-external</artifactId>
            <version>3.0.0</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.wutos.dloongsee.api.DLoongSeeApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>