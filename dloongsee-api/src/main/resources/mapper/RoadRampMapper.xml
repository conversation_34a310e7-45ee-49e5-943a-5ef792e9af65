<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.RoadRampMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.RoadRamp">
        <!--@mbg.generated-->
        <!--@Table road_ramp-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="mil" jdbcType="INTEGER" property="mil" />
        <result column="lnglats" jdbcType="LONGVARCHAR" property="lnglats" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, direction, `type`, mil, lnglats, segment_id
    </sql>
</mapper>