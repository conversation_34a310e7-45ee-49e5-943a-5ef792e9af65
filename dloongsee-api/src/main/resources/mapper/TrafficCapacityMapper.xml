<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.TrafficCapacityMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.TrafficCapacity">
        <!--@mbg.generated-->
        <!--@Table traffic_capacity-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="num" jdbcType="INTEGER" property="num" />
        <result column="ve" jdbcType="INTEGER" property="ve" />
        <result column="time_avg" jdbcType="DOUBLE" property="timeAvg" />
        <result column="speed_avg" jdbcType="DOUBLE" property="speedAvg" />
        <result column="congestion_level" jdbcType="INTEGER" property="congestionLevel" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, direction, num, ve, time_avg, speed_avg, congestion_level, start_mil, end_mil, segment_id,
        create_time
    </sql>

    <insert id="insertBatch">
        insert into traffic_capacity
        (direction, num, ve, time_avg, speed_avg, congestion_level, start_mil, end_mil,segment_id,create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.direction}, #{item.num}, #{item.ve}, #{item.timeAvg}, #{item.speedAvg}, #{item.congestionLevel},
            #{item.startMil}, #{item.endMil}, #{item.segmentId}, #{item.createTime})
        </foreach>
    </insert>

    <select id="getAvgTravelTimeBar" resultType="java.util.HashMap">
        SELECT
            road_segment.name AS roadSegment,
            SUM(CASE WHEN direction = 'UP' THEN time_avg ELSE 0 END) AS upTimeAvg,
            SUM(CASE WHEN direction = 'DOWN' THEN time_avg ELSE 0 END) AS downTimeAvg
        FROM traffic_capacity
        LEFT JOIN road_segment ON traffic_capacity.segment_id = road_segment.id
        WHERE create_time = #{date}
        GROUP BY segment_id
        ORDER BY segment_id
    </select>
</mapper>