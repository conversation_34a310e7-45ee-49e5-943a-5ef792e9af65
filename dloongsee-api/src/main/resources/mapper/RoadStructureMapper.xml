<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.RoadStructureMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.RoadStructure">
        <!--@mbg.generated-->
        <!--@Table road_structure-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="bridge_type" jdbcType="VARCHAR" property="bridgeType" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="lnglats" jdbcType="LONGVARCHAR" property="lnglats" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, `type`, bridge_type, direction, start_mil, end_mil, lnglats, segment_id
    </sql>
</mapper>