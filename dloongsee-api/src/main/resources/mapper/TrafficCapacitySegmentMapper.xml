<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.TrafficCapacitySegmentMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.TrafficCapacitySegment">
        <!--@mbg.generated-->
        <!--@Table traffic_capacity_segment-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, start_mil, end_mil, segment_id
    </sql>
</mapper>