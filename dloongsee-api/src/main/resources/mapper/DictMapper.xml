<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.DictMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.Dict">
        <!--@mbg.generated-->
        <!--@Table dict-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
        <result column="type_label" jdbcType="VARCHAR" property="typeLabel" />
        <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
        <result column="item_value" jdbcType="VARCHAR" property="itemValue" />
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="description" jdbcType="VARCHAR" property="description" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, type_code, type_label, item_code, item_value, sort_order, `status`, description
    </sql>
</mapper>