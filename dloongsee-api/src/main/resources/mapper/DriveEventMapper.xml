<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.DriveEventMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.DriveEvent">
        <!--@mbg.generated-->
        <!--@Table drive_event-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="car_id" jdbcType="VARCHAR" property="carId" />
        <result column="event_id" jdbcType="VARCHAR" property="eventId" />
        <result column="event_type" jdbcType="VARCHAR" property="eventType" />
        <result column="car_num" jdbcType="VARCHAR" property="carNum" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="dispose_time" jdbcType="TIMESTAMP" property="disposeTime" />
        <result column="dispose_status" jdbcType="INTEGER" property="disposeStatus" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="wn" jdbcType="INTEGER" property="wn" />
        <result column="speed" jdbcType="DOUBLE" property="speed" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, car_id, event_id, event_type, car_num, start_mil, end_mil, start_time, end_time,
        dispose_time, dispose_status, segment_id, direction, wn, speed
    </sql>

    <update id="updateEndTimeAndEndMil">
        update drive_event
        set end_time = #{endTime},
            end_mil  = #{endMil}
        where event_id = #{eventId}
    </update>

    <select id="countByEventType" resultType="com.wutos.dloongsee.api.vo.EventCountVO">
        select event_type, count(*) count from drive_event
        <where>
            <if test="segmentId != null">
                and segment_id = #{segmentId}
            </if>
            <if test="startTime != null">
                and start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and start_time &lt;= #{endTime}
            </if>
        </where>
        group by event_type
    </select>
</mapper>