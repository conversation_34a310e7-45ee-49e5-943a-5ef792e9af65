<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.TrafficCapacitySummaryMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.TrafficCapacitySummary">
        <!--@mbg.generated-->
        <!--@Table traffic_capacity_summary-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="direction" jdbcType="VARCHAR" property="direction" />
        <result column="segment_id" jdbcType="INTEGER" property="segmentId" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="num" jdbcType="INTEGER" property="num" />
        <result column="ve" jdbcType="INTEGER" property="ve" />
        <result column="time_avg" jdbcType="DOUBLE" property="timeAvg" />
        <result column="speed_avg" jdbcType="DOUBLE" property="speedAvg" />
        <result column="vc" jdbcType="DOUBLE" property="vc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, direction, segment_id, start_mil, end_mil, num, ve, time_avg, speed_avg, vc,
        create_time
    </sql>

    <insert id="insertBatch">
        insert into traffic_capacity_summary
        (direction, segment_id, start_mil, end_mil, num, ve, time_avg, speed_avg, vc, create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.direction}, #{item.segmentId}, #{item.startMil}, #{item.endMil}, #{item.num},
            #{item.ve},#{item.timeAvg}, #{item.speedAvg}, #{item.vc}, #{item.createTime})
        </foreach>
    </insert>
</mapper>