<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wutos.dloongsee.api.mapper.RoadSegmentMapper">
    <resultMap id="BaseResultMap" type="com.wutos.dloongsee.api.entity.RoadSegment">
        <!--@mbg.generated-->
        <!--@Table road_segment-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="lane_count" jdbcType="INTEGER" property="laneCount" />
        <result column="start_mil" jdbcType="INTEGER" property="startMil" />
        <result column="end_mil" jdbcType="INTEGER" property="endMil" />
        <result column="lnglats" jdbcType="LONGVARCHAR" property="lnglats" />
        <result column="has_emergency_lane" jdbcType="TINYINT" property="hasEmergencyLane" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, lane_count, start_mil, end_mil, lnglats, has_emergency_lane
    </sql>
</mapper>