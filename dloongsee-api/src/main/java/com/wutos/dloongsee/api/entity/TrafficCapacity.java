package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

/**
 * <p>
 * 通行能力
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
@Data
@TableName(value = "traffic_capacity")
public class TrafficCapacity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 车流量
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * 当量交通量
     */
    @TableField(value = "ve")
    private Integer ve;

    /**
     * 平均行程时间(s)
     */
    @TableField(value = "time_avg")
    private Double timeAvg;

    /**
     * 平均行程速度
     */
    @TableField(value = "speed_avg")
    private Double speedAvg;

    /**
     * 拥堵等级
     */
    @TableField(value = "congestion_level")
    private Integer congestionLevel;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;

    /**
     * 时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}