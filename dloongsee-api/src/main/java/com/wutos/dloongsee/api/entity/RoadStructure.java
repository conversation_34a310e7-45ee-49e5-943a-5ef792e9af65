package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.BridgeType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.StructureType;
import lombok.Data;

/**
 * <p>
 * 道路基础设施数据
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
@TableName(value = "road_structure")
public class RoadStructure {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名字
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 类型: 桥梁、互通、边坡
     */
    @TableField(value = "`type`")
    private StructureType type;

    /**
     * 桥梁类型
     */
    @TableField(value = "bridge_type")
    private BridgeType bridgeType;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 经纬度数据
     */
    @TableField(value = "lnglats")
    private String lnglats;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;
}