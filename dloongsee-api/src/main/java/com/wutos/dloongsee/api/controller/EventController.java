package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.controller.reqeust.EventRequest;
import com.wutos.dloongsee.api.service.EventService;
import com.wutos.dloongsee.api.vo.*;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/event")
public class EventController {

    @Autowired
    private EventService eventService;

    @PostMapping("/page")
    public PageResponse<EventVO> getEvent(@RequestBody EventRequest eventRequest) {
        return eventService.findByRequest(eventRequest);
    }

    @GetMapping("/category/all")
    public BaseResponse<List<EventCategoryVO>> getAllEventCategory() {
        return BaseResponse.ok(Arrays.stream(EventCategory.values()).map(category ->
                        new EventCategoryVO(category.getLabel(), category))
                .collect(Collectors.toList()));
    }

    @GetMapping("/type/all")
    public BaseResponse<List<EventTypeVO>> getAllEventType() {
        return BaseResponse.ok(Arrays.stream(EventType.values()).map(eventType ->
                        EventTypeVO.builder()
                                .label(eventType.getLabel())
                                .type(eventType)
                                .category(eventType.getCategory())
                                .build())
                .collect(Collectors.toList()));
    }
}
