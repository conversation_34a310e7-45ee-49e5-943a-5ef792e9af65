package com.wutos.dloongsee.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

/**
 * <p>
 * 通行能力配置
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
@Data
@ConfigurationProperties(prefix = "traffic-capacity")
public class TrafficCapacityConfig {
    // cron
    private String cron;
    // 统计时长(秒)
    private Integer duration;
    // 自由流速度
    private Double freeSpeed;
    // 堵车阈值
    private Map<Integer, CongestionThreshold> congestionLevel;

    @Data
    public static class CongestionThreshold {
        private Double speed;
    }
}
