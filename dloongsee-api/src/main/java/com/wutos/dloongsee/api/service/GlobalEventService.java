package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wutos.dloongsee.api.entity.DriveEvent;
import com.wutos.dloongsee.api.entity.PassEvent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.mapper.DriveEventMapper;
import com.wutos.dloongsee.api.mapper.PassEventMapper;
import com.wutos.dloongsee.api.vo.*;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class GlobalEventService {
    @Autowired
    private PassEventMapper passEventMapper;
    @Autowired
    private DriveEventMapper driveEventMapper;

    @Autowired
    private RoadService roadService;

    private List<RoadSegment> roadSegments;


    @PostConstruct
    public void init() {
        roadSegments = roadService.getRoadSegments();
    }

    /**
     * 获取当日事件Summary统计数据
     *
     * @return
     */
    public TrafficEventSummaryVO getEventDataSummaryByDay() {
        TrafficEventSummaryVO eventSummary = new TrafficEventSummaryVO();
        List<PassEvent> passEventList = this.getPassEventListByDay(null);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(null);
        eventSummary.setEventTotal(passEventList.size() + driveEventList.size());
        //严重事件
        Long passSeriousTotal = passEventList.stream().filter(e -> e.getEventType() == EventType.PEDESTRIAN || e.getEventType() == EventType.SPRINKLE).count();
        Long driveSeriousTotal = driveEventList.stream().filter(e -> e.getEventType() == EventType.STOP).count();
        Integer eventCount = passEventList.size() + driveEventList.size();
        eventSummary.setSeriousTotal(Math.toIntExact((passSeriousTotal + driveSeriousTotal)));
        if (eventCount > 0) {
            eventSummary.setSeriousRatio((passSeriousTotal + driveSeriousTotal) / 1.0 / eventCount * 100);
        } else {
            eventSummary.setSeriousRatio(0.0);
        }

        Long passEventUntreatedTotal = passEventList.stream().filter(e -> e.getDisposeStatus() != null && e.getDisposeStatus() == 0).count();
        Long driveEventUntreatedTotal = driveEventList.stream().filter(e -> e.getDisposeStatus() != null && e.getDisposeStatus() == 0).count();
        eventSummary.setUntreatedTotal(Math.toIntExact((passEventUntreatedTotal + driveEventUntreatedTotal)));
        return eventSummary;
    }


    /**
     * 取当日事件分类统计数据
     *
     * @return
     */
    public List<TrafficEventTypeAnalysisVO> getEventTypeAnalysisByDay() {
        List<TrafficEventTypeAnalysisVO> resultList = new ArrayList<>();
        List<PassEvent> passEventList = this.getPassEventListByDay(null);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(null);
        Map<EventType, Long> resultMap = this.getHighEventGroupByType(passEventList, driveEventList);
        for (EventType key : resultMap.keySet()) {
            TrafficEventTypeAnalysisVO trafficEventTypeAnalysisVO = new TrafficEventTypeAnalysisVO();
            trafficEventTypeAnalysisVO.setType(key.name());
            trafficEventTypeAnalysisVO.setTypeName(key.getLabel());
            trafficEventTypeAnalysisVO.setCount(Math.toIntExact(resultMap.get(key)));
            resultList.add(trafficEventTypeAnalysisVO);
        }
        return resultList;
    }

    /**
     * 获取事件高发路段列表
     *
     * @return
     */
    public List<HighEventSegmentVO> getHighEventSegmentByDay() {
        List<HighEventSegmentVO> resultList = new ArrayList<>();
        List<PassEvent> passEventList = this.getPassEventListByDay(null);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(null);
        Map<Integer, Long> resultMap = this.getHighEventGroupBySegment(passEventList, driveEventList);
        roadSegments.forEach(e -> {
            if (!resultMap.containsKey(e.getId())) {
                resultMap.put(e.getId(), 0L);
            }
        });
        for (Integer key : resultMap.keySet()) {
            Optional<RoadSegment> optional = roadSegments.stream().filter(e -> e.getId().equals(key)).findFirst();
            HighEventSegmentVO highEventSegmentVO = new HighEventSegmentVO();
            if (optional.isPresent()) {
                highEventSegmentVO.setSegmentId(key);
                highEventSegmentVO.setSegmentName(optional.get().getName());
                highEventSegmentVO.setEventTotal(Math.toIntExact(resultMap.get(key)));
                resultList.add(highEventSegmentVO);
            }
        }
        return resultList.stream().sorted(Comparator.comparing(HighEventSegmentVO::getEventTotal).reversed()).collect(Collectors.toList());
    }


    /**
     * 事件高发时段
     *
     * @return
     */
    public List<HighEventTimeVO> getHighEventTimeByDay() {
        List<HighEventTimeVO> highEventTimeVOList = new ArrayList<>();
        LocalDateTime startTimeOfDay = LocalDate.now().atStartOfDay();
        List<PassEvent> passEventList = this.getPassEventListByDay(null);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(null);
        int eventCount = passEventList.size() + driveEventList.size();
        Map<Integer, Long> resultMap = this.getHighEventGroupByTime(passEventList, driveEventList);
        IntStream.range(0, 24).filter(hour -> !resultMap.containsKey(hour)).forEach(hour -> resultMap.put(hour, 0L));
        for (Integer key : resultMap.keySet()) {
            HighEventTimeVO highEventTimeVO = new HighEventTimeVO();
            highEventTimeVO.setStartTime(startTimeOfDay.plusHours(key));
            highEventTimeVO.setEndTime(startTimeOfDay.plusHours(key + 1));
            if (eventCount > 0) {
                highEventTimeVO.setEventRatio(resultMap.get(key) / 1.0 / eventCount * 100);
            } else {
                highEventTimeVO.setEventRatio(0.0);
            }
            highEventTimeVOList.add(highEventTimeVO);
        }
        return highEventTimeVOList.stream().sorted(Comparator.comparing(HighEventTimeVO::getEventRatio).reversed()).collect(Collectors.toList());
    }

    /**
     * 事件统计数据
     *
     * @return
     */
    public List<EventStatisticsVO> getEventStatisticsByDay(Integer roadSegmentId) {
        List<EventStatisticsVO> eventStatisticsList = new ArrayList<>();
        List<PassEvent> passEventList = this.getPassEventListByDay(roadSegmentId);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(roadSegmentId);
        for (RoadDirection direction : RoadDirection.values()) {
            List<PassEvent> passEvents = passEventList.stream().filter(e -> e.getDirection() == direction).collect(Collectors.toList());
            List<DriveEvent> driveEvents = driveEventList.stream().filter(e -> e.getDirection() == direction).collect(Collectors.toList());
            EventStatisticsVO eventStatisticsVO = new EventStatisticsVO();
            eventStatisticsVO.setDirection(direction);
            Integer eventCount = passEvents.size() + driveEvents.size();
            eventStatisticsVO.setEventTotal(eventCount);
            if (eventCount > 0) {
                //高发事件
                EventType eventMaxKey = null;
                Long eventMaxCount = 0L;
                Map<EventType, Long> highEventGroupMap = this.getHighEventGroupByType(passEvents, driveEvents);
                for (EventType type : highEventGroupMap.keySet()) {
                    if (eventMaxCount < highEventGroupMap.get(type)) {
                        eventMaxCount = highEventGroupMap.get(type);
                        eventMaxKey = type;
                    }
                }
                if (eventMaxKey != null) {
                    eventStatisticsVO.setHighEventTypeName(eventMaxKey.getLabel());
                    eventStatisticsVO.setHighEventTypeRatio((highEventGroupMap.get(eventMaxKey)) / 1.0 / eventCount * 100);
                }
                //处理率
                Long disposeCount = passEvents.stream().filter(e -> e.getDisposeStatus() == 0).count() + driveEvents.stream().filter(e -> e.getDisposeStatus() == 0).count();
                eventStatisticsVO.setDisposeRatio(disposeCount / 1.0 / eventCount * 100);
                Map<Integer, Long> timeGroupMap = this.getHighEventGroupByTime(passEvents, driveEvents);
                Integer timeMapMaxKey = 0;
                Long timeMapMaxCount = 0L;
                for (Integer key : timeGroupMap.keySet()) {
                    if (timeMapMaxCount < timeGroupMap.get(key)) {
                        timeMapMaxCount = timeGroupMap.get(key);
                        timeMapMaxKey = key;
                    }
                }
                LocalDateTime startTimeOfDay = LocalDate.now().atStartOfDay();
                eventStatisticsVO.setHighEventTime(startTimeOfDay.plusHours(timeMapMaxKey));
                Map<Integer, Long> segmentGroupMap = this.getHighEventGroupBySegment(passEvents, driveEvents);
                Integer segmentMapMaxKey = 0;
                Long segmentMapMaxCount = 0L;
                for (Integer key : segmentGroupMap.keySet()) {
                    if (segmentMapMaxCount < segmentGroupMap.get(key)) {
                        segmentMapMaxCount = segmentGroupMap.get(key);
                        segmentMapMaxKey = key;
                    }
                }
                Integer finalSegmentMapMaxKey = segmentMapMaxKey;
                Optional<RoadSegment> optional = roadSegments.stream().filter(e -> e.getId().equals(finalSegmentMapMaxKey)).findFirst();
                if (optional.isPresent()) {
                    eventStatisticsVO.setHighEventSegment(optional.get().getName());
                    eventStatisticsVO.setHighEventSegmentId(optional.get().getId());
                }
            }
            eventStatisticsList.add(eventStatisticsVO);
        }
        return eventStatisticsList;
    }


    /**
     * 事件时空分布图数据
     *
     * @return
     */
    public List<EventSpaceTimeStatisticsVO> getEventSpaceTimeStatisticsByDay() {
        List<EventSpaceTimeStatisticsVO> eventSpaceTimeStatisticsList = new ArrayList<>();
        List<PassEvent> passEventList = this.getPassEventListByDay(null);
        List<DriveEvent> driveEventList = this.getDriveEventListByDay(null);
        List<EventSpaceTimeStatisticsVO> tempEventList = new ArrayList<>();
        tempEventList.addAll(passEventList.stream().map(e -> {
            EventSpaceTimeStatisticsVO eventSpaceTimeStatistics = new EventSpaceTimeStatisticsVO();
            eventSpaceTimeStatistics.setStartMil(e.getStartMil());
            eventSpaceTimeStatistics.setStartTime(e.getStartTime());
            return eventSpaceTimeStatistics;
        }).collect(Collectors.toList()));
        tempEventList.addAll(driveEventList.stream().map(e -> {
            EventSpaceTimeStatisticsVO eventSpaceTimeStatistics = new EventSpaceTimeStatisticsVO();
            eventSpaceTimeStatistics.setStartMil(e.getStartMil());
            eventSpaceTimeStatistics.setStartTime(e.getStartTime());
            return eventSpaceTimeStatistics;
        }).collect(Collectors.toList()));
        Map<Integer, List<EventSpaceTimeStatisticsVO>> eventCollects = tempEventList.stream().collect(Collectors.groupingBy(e -> e.getStartTime().getHour()));
        LocalDateTime startTimeOfDay = LocalDate.now().atStartOfDay();
        for (Integer key : eventCollects.keySet()) {
            List<EventSpaceTimeStatisticsVO> tempList = eventCollects.get(key);
            Map<Integer, Long> collect = tempList.stream().collect(Collectors.groupingBy(e -> e.getStartMil() / 1000, Collectors.counting()));
            for (Integer milKey : collect.keySet()) {
                EventSpaceTimeStatisticsVO eventSpaceTimeStatisticsVO = new EventSpaceTimeStatisticsVO();
                eventSpaceTimeStatisticsVO.setEventCount(Math.toIntExact(collect.get(milKey)));
                eventSpaceTimeStatisticsVO.setStartTime(startTimeOfDay.plusHours(key));
                eventSpaceTimeStatisticsVO.setStartMil(milKey);
                eventSpaceTimeStatisticsList.add(eventSpaceTimeStatisticsVO);
            }
        }
        return eventSpaceTimeStatisticsList;
    }


    private Map<EventType, Long> getHighEventGroupByType(List<PassEvent> passEventList, List<DriveEvent> driveEventList) {
        Map<EventType, Long> passEventCollects = passEventList.stream().collect(Collectors.groupingBy(PassEvent::getEventType, Collectors.counting()));
        Map<EventType, Long> driveEventCollects = driveEventList.stream().collect(Collectors.groupingBy(DriveEvent::getEventType, Collectors.counting()));
        Map<EventType, Long> resultMap = new HashMap<>(passEventCollects);
        resultMap.putAll(driveEventCollects);
        return resultMap;
    }

    private Map<Integer, Long> getHighEventGroupByTime(List<PassEvent> passEventList, List<DriveEvent> driveEventList) {
        Map<Integer, Long> passEventCollects = passEventList.stream().collect(Collectors.groupingBy(event -> event.getStartTime().getHour(), Collectors.counting()));
        Map<Integer, Long> driveEventCollects = driveEventList.stream().collect(Collectors.groupingBy(event -> event.getStartTime().getHour(), Collectors.counting()));
        Map<Integer, Long> resultMap = new HashMap<>(passEventCollects);
        driveEventCollects.forEach((key, value) -> {
            resultMap.merge(key, value, Long::sum);
        });
        return resultMap;
    }

    private Map<Integer, Long> getHighEventGroupBySegment(List<PassEvent> passEventList, List<DriveEvent> driveEventList) {
        Map<Integer, Long> passEventCollects = passEventList.stream().collect(Collectors.groupingBy(PassEvent::getSegmentId, Collectors.counting()));
        Map<Integer, Long> driveEventCollects = driveEventList.stream().collect(Collectors.groupingBy(DriveEvent::getSegmentId, Collectors.counting()));
        Map<Integer, Long> resultMap = new HashMap<>(passEventCollects);
        driveEventCollects.forEach((key, value) -> {
            resultMap.merge(key, value, Long::sum);
        });
        return resultMap;
    }

    private List<PassEvent> getPassEventListByDay(Integer roadSegmentId) {
        LambdaQueryWrapper<PassEvent> passQueryWrapper = new LambdaQueryWrapper();
        passQueryWrapper.gt(PassEvent::getStartTime, LocalDate.now());
        if (roadSegmentId != null && roadSegmentId != -1) {
            passQueryWrapper.eq(PassEvent::getSegmentId, roadSegmentId);
        }
        List<PassEvent> passEventList = passEventMapper.selectList(passQueryWrapper);
        return passEventList;
    }

    private List<DriveEvent> getDriveEventListByDay(Integer roadSegmentId) {
        LambdaQueryWrapper<DriveEvent> driveQueryWrapper = new LambdaQueryWrapper();
        driveQueryWrapper.gt(DriveEvent::getStartTime, LocalDate.now());
        if (roadSegmentId != null && roadSegmentId != -1) {
            driveQueryWrapper.eq(DriveEvent::getSegmentId, roadSegmentId);
        }
        List<DriveEvent> driveEventList = driveEventMapper.selectList(driveQueryWrapper);
        return driveEventList;
    }
}
