package com.wutos.dloongsee.api.mock;

import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.springevent.ZMQEventSpringEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <p>
 * 模拟事件接口
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
@RestController
@RequestMapping("/mock/event")
public class MockEvent {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @PostMapping
    public String publish(@RequestBody ZMQEventDTO zmqEventDTO) {
        try {
            RoadDirection roadDirection = zmqEventDTO.getRoadDirection();
            if (roadDirection == null) return "eventId中需要方向";
            applicationEventPublisher.publishEvent(new ZMQEventSpringEvent(zmqEventDTO));
            return "success";
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            return sw.toString();
        }
    }
}
