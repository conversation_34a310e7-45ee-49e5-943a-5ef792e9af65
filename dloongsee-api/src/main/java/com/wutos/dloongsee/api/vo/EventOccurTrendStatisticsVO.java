package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 事件发生趋势统计 VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventOccurTrendStatisticsVO {

    private String period;

    private Long driveEventCount;

    private Long passEventCount;

    private Long trafficEventCount;
}
