package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wutos.dloongsee.api.config.TrafficCapacityConfig;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficCapacitySummary;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySummaryMapper;
import com.wutos.dloongsee.api.vo.TrafficInfoVO;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.TrafficCapacityLevel;
import com.wutos.dloongsee.common.utils.TaskTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 交通态势
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@Service
public class TrafficService {

    @Autowired
    private TrafficCapacityConfig config;
    @Autowired
    private TrafficCapacitySummaryMapper trafficCapacitySummaryMapper;
    @Autowired
    private TrafficCapacityService trafficCapacityService;
    @Autowired
    private RoadService roadService;

    /**
     * 交通流信息(通行能力、拥堵指数、行程时间)
     *
     * @return TrafficInfoVO
     */
    public TrafficInfoVO getTrafficInfo(Integer segmentId) {
        // 获取当天车流总量
        LambdaQueryWrapper<TrafficCapacitySummary> todayQueryWrapper = new LambdaQueryWrapper<>();
        todayQueryWrapper.eq(TrafficCapacitySummary::getSegmentId, segmentId);
        todayQueryWrapper.gt(TrafficCapacitySummary::getCreateTime, LocalDate.now().atStartOfDay());
        todayQueryWrapper.le(TrafficCapacitySummary::getCreateTime, LocalDateTime.now());
        List<TrafficCapacitySummary> todayTrafficCapacitySummaries = trafficCapacitySummaryMapper.selectList(todayQueryWrapper);
        int todayTotalTrafficFlow = todayTrafficCapacitySummaries.stream().mapToInt(TrafficCapacitySummary::getNum).sum();

        // 获取最近一次计算的时间，来获取通行能力等级、拥堵指数、行程时间
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());
        LambdaQueryWrapper<TrafficCapacitySummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TrafficCapacitySummary::getCreateTime, lastExecutionTime);
        queryWrapper.eq(TrafficCapacitySummary::getSegmentId, segmentId);
        List<TrafficCapacitySummary> trafficCapacitySummaries = trafficCapacitySummaryMapper.selectList(queryWrapper);
        if (trafficCapacitySummaries == null || trafficCapacitySummaries.isEmpty()) {
            return TrafficInfoVO.builder()
                    .todayTotalTrafficFlow(todayTotalTrafficFlow)
                    .trafficCapacityLevel(TrafficCapacityLevel.A)
                    .trafficCapacityLevelLabel(TrafficCapacityLevel.A.getLabel())
                    .congestionIndex(4)
                    .upTravelTime(0)
                    .downTravelTime(0)
                    .build();
        }

        TrafficCapacitySummary up = trafficCapacitySummaries.stream().filter(e -> e.getDirection() == RoadDirection.UP).findFirst().get();
        TrafficCapacitySummary down = trafficCapacitySummaries.stream().filter(e -> e.getDirection() == RoadDirection.DOWN).findFirst().get();
        // 上下行 vc 比最大值
        TrafficCapacityLevel trafficCapacityLevel = TrafficCapacityLevel.getLevel(trafficCapacitySummaries.stream().mapToDouble(TrafficCapacitySummary::getVc).max().getAsDouble());
        // 上下行拥堵指数最严重
        RoadSegment roadSegment = roadService.getRoadSegmentById(segmentId);
        if (roadSegment == null) {
            roadSegment = roadService.getRoadSegments().get(0);
        }
        int laneCount = roadSegment.getHasEmergencyLane() == 1 ? roadSegment.getLaneCount() - 1 : roadSegment.getLaneCount();
        int upCongestionLevel = trafficCapacityService.getCongestionLevel(up.getNum(), laneCount, up.getSpeedAvg());
        int downCongestionLevel = trafficCapacityService.getCongestionLevel(down.getNum(), laneCount, down.getSpeedAvg());
        int congestionIndex = Math.min(upCongestionLevel, downCongestionLevel);

        return TrafficInfoVO.builder()
                .todayTotalTrafficFlow(todayTotalTrafficFlow)
                .trafficCapacityLevel(trafficCapacityLevel)
                .trafficCapacityLevelLabel(trafficCapacityLevel.getLabel())
                .congestionIndex(congestionIndex)
                .upTravelTime((int) Math.round(up.getTimeAvg() / 60))
                .downTravelTime((int) Math.round(down.getTimeAvg() / 60))
                .build();
    }
}
