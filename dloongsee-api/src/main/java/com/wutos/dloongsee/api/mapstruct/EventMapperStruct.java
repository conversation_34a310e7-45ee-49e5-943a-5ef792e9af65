package com.wutos.dloongsee.api.mapstruct;

import com.wutos.dloongsee.api.entity.DriveEvent;
import com.wutos.dloongsee.api.entity.PassEvent;
import com.wutos.dloongsee.api.entity.TrafficEvent;
import com.wutos.dloongsee.api.service.RoadService;
import com.wutos.dloongsee.api.vo.EventVO;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.utils.MilUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring", uses = {RoadService.class})
public interface EventMapperStruct {

    @Mappings({
            @Mapping(target = "typeName", source = "eventType"),
            @Mapping(target = "displayMil", source = "startMil"),
            @Mapping(target = "segmentName", source = "segmentId", qualifiedByName = "getRoadSegmentNameById")
    })
    EventVO toVO(DriveEvent driveEvent);

    @Mappings({
            @Mapping(target = "typeName", source = "eventType"),
            @Mapping(target = "displayMil", source = "startMil"),
            @Mapping(target = "segmentName", source = "segmentId", qualifiedByName = "getRoadSegmentNameById")
    })
    EventVO toVO(PassEvent passEvent);

    @Mappings({
            @Mapping(target = "typeName", source = "eventType"),
            @Mapping(target = "displayMil", source = "startMil"),
            @Mapping(target = "segmentName", source = "segmentId", qualifiedByName = "getRoadSegmentNameById")
    })
    EventVO toVO(TrafficEvent trafficEvent);

    default String mapEventTypeToTypeName(EventType eventType) {
        return eventType != null ? eventType.getLabel() : null;
    }

    default String mapStartMilToDisplayMil(Integer startMil) {
        return MilUtil.convertMilToString(startMil);
    }
}
