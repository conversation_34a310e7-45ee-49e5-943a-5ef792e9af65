package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 通行障碍事件
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/21
 */
@Data
@TableName(value = "pass_event")
public class PassEvent {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 算法输出的事件id
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 事件类型（检测到行人/行车通行/占用）
     */
    @TableField(value = "event_type")
    private EventType eventType;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 处置时间
     */
    @TableField(value = "dispose_time")
    private LocalDateTime disposeTime;

    /**
     * 处置状态
     */
    @TableField(value = "dispose_status")
    private Integer disposeStatus;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 车道
     */
    @TableField(value = "wn")
    private Integer wn;
}