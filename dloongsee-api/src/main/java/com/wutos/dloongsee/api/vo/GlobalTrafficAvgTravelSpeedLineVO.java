package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GlobalTrafficAvgTravelSpeedLineVO {

    /**
     * 里程点位
     */
    private List<Integer> milPoints;

    /**
     * 上行平均速度
     */
    private List<Double> upSpeeds;

    /**
     * 下行平均速度
     */
    private List<Double> downSpeeds;

}
