package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 事件统计VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventTotalStatisticsVO {
    
    /**
     * 流量失衡类事件数量
     */
    private Long trafficEventCount;
    
    /**
     * 违规驾驶类事件数量
     */
    private Long driveEventCount;
    
    /**
     * 通行障碍类事件数量
     */
    private Long passEventCount;
    
    /**
     * 违规驾驶类车辆占比（百分比）
     */
    private BigDecimal driveVehicleRatio;

}
