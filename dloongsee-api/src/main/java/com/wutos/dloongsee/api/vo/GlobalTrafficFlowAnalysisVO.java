package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GlobalTrafficFlowAnalysisVO {

    /**
     * 时间序列
     */
    private List<String> times;

    /**
     * 上行车流量
     */
    private List<Integer> upCarFlow;

    /**
     * 下行车流量
     */
    private List<Integer> downCarFlow;

}
