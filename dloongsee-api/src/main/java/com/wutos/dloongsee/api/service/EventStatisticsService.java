package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wutos.dloongsee.api.entity.*;
import com.wutos.dloongsee.api.mapper.CarTrackMapper;
import com.wutos.dloongsee.api.mapper.DriveEventMapper;
import com.wutos.dloongsee.api.mapper.PassEventMapper;
import com.wutos.dloongsee.api.mapper.TrafficEventMapper;
import com.wutos.dloongsee.api.vo.*;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 事件统计服务
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Slf4j
@Service
public class EventStatisticsService {

    @Autowired
    private TrafficEventMapper trafficEventMapper;

    @Autowired
    private DriveEventMapper driveEventMapper;

    @Autowired
    private PassEventMapper passEventMapper;

    @Autowired
    private CarTrackMapper carTrackMapper;

    @Autowired
    private RoadService roadService;

    /**
     * 获取事件总体情况统计
     *
     * @param type 统计类型：day（当天）、month（当月）、year（当年）
     * @return 事件统计结果
     */
    public EventTotalStatisticsVO getEventStatistics(String type) {
        LocalDateTime startTime;
        LocalDateTime endTime;
        LocalDate now = LocalDate.now();

        // 根据类型确定时间范围
        switch (type) {
            case "day":
                // 统计当天
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
                break;
            case "month":
                // 统计当月
                LocalDate firstDayOfMonth = now.withDayOfMonth(1);
                startTime = firstDayOfMonth.atStartOfDay();
                endTime = now.withDayOfMonth(now.lengthOfMonth()).atTime(LocalTime.MAX);
                break;
            case "year":
                // 统计当年
                LocalDate firstDayOfYear = now.withDayOfYear(1);
                startTime = firstDayOfYear.atStartOfDay();
                endTime = now.withDayOfYear(now.lengthOfYear()).atTime(LocalTime.MAX);
                break;
            default:
                startTime = now.atStartOfDay();
                endTime = now.atTime(LocalTime.MAX);
                break;
        }

        log.info("统计事件数据，时间范围：{} - {}", startTime, endTime);

        // 统计流量失衡类事件数量
        Long trafficEventCount = countTrafficEvents(startTime, endTime);

        // 统计违规驾驶类事件数量
        Long driveEventCount = countDriveEvents(startTime, endTime);

        // 统计通行障碍类事件数量
        Long passEventCount = countPassEvents(startTime, endTime);

        // 统计时间范围内轨迹车辆总数
        Long totalVehicleCount = countTotalVehicles(startTime, endTime);

        // 统计违规驾驶类涉及车辆数
        Long driveVehicleCount = countDriveVehicles(startTime, endTime);

        // 计算违规驾驶类车辆占比（保留一位小数）
        BigDecimal driveVehicleRatio = BigDecimal.ZERO;
        if (totalVehicleCount > 0) {
            driveVehicleRatio = BigDecimal.valueOf(driveVehicleCount)
                    .divide(BigDecimal.valueOf(totalVehicleCount), 3, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(1, RoundingMode.HALF_UP);
        }

        return EventTotalStatisticsVO.builder()
                .trafficEventCount(trafficEventCount)
                .driveEventCount(driveEventCount)
                .passEventCount(passEventCount)
                .driveVehicleRatio(driveVehicleRatio)
                .build();
    }

    /**
     * 统计流量失衡类事件数量
     */
    private Long countTrafficEvents(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<TrafficEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(TrafficEvent::getStartTime, startTime)
                .le(TrafficEvent::getStartTime, endTime);
        return trafficEventMapper.selectCount(wrapper);
    }

    /**
     * 统计违规驾驶类事件数量
     */
    private Long countDriveEvents(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<DriveEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(DriveEvent::getStartTime, startTime)
                .le(DriveEvent::getStartTime, endTime);
        return driveEventMapper.selectCount(wrapper);
    }

    /**
     * 统计通行障碍类事件数量
     */
    private Long countPassEvents(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<PassEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(PassEvent::getStartTime, startTime)
                .le(PassEvent::getStartTime, endTime);
        return passEventMapper.selectCount(wrapper);
    }

    /**
     * 统计时间范围内轨迹车辆总数
     */
    private Long countTotalVehicles(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<CarTrack> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(CarTrack::getStartTime, startTime)
                .le(CarTrack::getStartTime, endTime);
        return carTrackMapper.selectCount(wrapper);
    }

    /**
     * 统计违规驾驶类涉及车辆数（去重）
     */
    private Long countDriveVehicles(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<DriveEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(DriveEvent::getStartTime, startTime)
                .le(DriveEvent::getStartTime, endTime)
                .isNotNull(DriveEvent::getCarId);

        List<DriveEvent> driveEvents = driveEventMapper.selectList(wrapper);
        
        // 使用Set去重车辆ID
        Set<String> uniqueCarIds = new HashSet<>();
        for (DriveEvent event : driveEvents) {
            if (event.getCarId() != null && !event.getCarId().trim().isEmpty()) {
                uniqueCarIds.add(event.getCarId());
            }
        }
        
        return (long) uniqueCarIds.size();
    }

    /**
     * 获取近7日高发事件统计（优化版本）
     *
     * @return 近7日每天各类事件的统计数据
     */
    public List<SevenDayEventDateStatisticsVO> getRecentSevenDaysEventStats() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6); // 包含今天在内的近7天
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = endDate.atTime(LocalTime.MAX);

        // 一次性查询近7天的所有事件数据
        List<TrafficEvent> trafficEvents = getTrafficEventsByTimeRange(startTime, endTime);
        List<DriveEvent> driveEvents = getDriveEventsByTimeRange(startTime, endTime);
        List<PassEvent> passEvents = getPassEventsByTimeRange(startTime, endTime);

        // 按日期和事件类型分组统计
        Map<String, Map<EventType, Long>> dailyEventCounts = new HashMap<>();

        // 初始化每天的数据结构
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            String dateStr = date.toString();
            dailyEventCounts.put(dateStr, new HashMap<>());
            // 初始化所有事件类型为0
            for (EventType eventType : EventType.values()) {
                dailyEventCounts.get(dateStr).put(eventType, 0L);
            }
        }

        // 统计流量事件
        trafficEvents.forEach(event -> {
            String dateStr = event.getStartTime().toLocalDate().toString();
            if (dailyEventCounts.containsKey(dateStr)) {
                EventType eventType = event.getEventType();
                dailyEventCounts.get(dateStr).merge(eventType, 1L, Long::sum);
            }
        });

        // 统计驾驶事件
        driveEvents.forEach(event -> {
            String dateStr = event.getStartTime().toLocalDate().toString();
            if (dailyEventCounts.containsKey(dateStr)) {
                EventType eventType = event.getEventType();
                dailyEventCounts.get(dateStr).merge(eventType, 1L, Long::sum);
            }
        });

        // 统计通行障碍事件
        passEvents.forEach(event -> {
            String dateStr = event.getStartTime().toLocalDate().toString();
            if (dailyEventCounts.containsKey(dateStr)) {
                EventType eventType = event.getEventType();
                dailyEventCounts.get(dateStr).merge(eventType, 1L, Long::sum);
            }
        });

        // 构建返回结果
        List<SevenDayEventDateStatisticsVO> result = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            String dateStr = date.toString();
            Map<EventType, Long> eventCounts = dailyEventCounts.get(dateStr);

            SevenDayEventDateStatisticsVO dayStats = SevenDayEventDateStatisticsVO.builder()
                    .date(dateStr)
                    .mainCongestionCount(eventCounts.get(EventType.CONGESTION_MAIN))
                    .slaveCongestionCount(eventCounts.get(EventType.CONGESTION_SLAVE))
                    .trafficSurgeCount(eventCounts.get(EventType.TRAFFIC_SURGE))
                    .speedDiffCount(eventCounts.get(EventType.SPEED_DIFF))
                    .stopCount(eventCounts.get(EventType.STOP))
                    .overSpeedCount(eventCounts.get(EventType.OVER_SPEED))
                    .backDriveCount(eventCounts.get(EventType.BACK_DRIVE))
                    .emergencyLaneCount(eventCounts.get(EventType.EMERGENCY_LANE))
                    .sprinkleCount(eventCounts.get(EventType.SPRINKLE))
                    .pedestrianCount(eventCounts.get(EventType.PEDESTRIAN))
                    .driveLaneOccupyCount(eventCounts.get(EventType.DRIVE_LANE_OCCUPY))
                    .lowSpeedCount(eventCounts.get(EventType.LOW_SPEED))
                    .build();

            result.add(dayStats);
        }

        return result;
    }

    /**
     * 根据事件类型统计指定时间范围内的事件数量
     */
    private Long countEventsByType(EventType eventType, LocalDateTime startTime, LocalDateTime endTime) {
        switch (eventType.getCategory()) {
            case TRAFFIC:
                return countTrafficEventsByType(eventType, startTime, endTime);
            case DRIVE:
                return countDriveEventsByType(eventType, startTime, endTime);
            case PASS:
                return countPassEventsByType(eventType, startTime, endTime);
            default:
                return 0L;
        }
    }

    /**
     * 统计指定类型的流量事件数量
     */
    private Long countTrafficEventsByType(EventType eventType, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<TrafficEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrafficEvent::getEventType, eventType)
                .ge(TrafficEvent::getStartTime, startTime)
                .le(TrafficEvent::getStartTime, endTime);
        return trafficEventMapper.selectCount(wrapper);
    }

    /**
     * 统计指定类型的驾驶事件数量
     */
    private Long countDriveEventsByType(EventType eventType, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<DriveEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DriveEvent::getEventType, eventType)
                .ge(DriveEvent::getStartTime, startTime)
                .le(DriveEvent::getStartTime, endTime);
        return driveEventMapper.selectCount(wrapper);
    }

    /**
     * 统计指定类型的通行障碍事件数量
     */
    private Long countPassEventsByType(EventType eventType, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<PassEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PassEvent::getEventType, eventType)
                .ge(PassEvent::getStartTime, startTime)
                .le(PassEvent::getStartTime, endTime);
        return passEventMapper.selectCount(wrapper);
    }

    /**
     * 获取事件发生趋势统计
     *
     * @param type 统计类型：month（近12个月）、year（近5年）
     * @return 事件发生趋势统计数据
     */
    public List<EventOccurTrendStatisticsVO> getEventOccurTrendStatistics(String type) {
        List<EventOccurTrendStatisticsVO> result = new ArrayList<>();

        if ("month".equals(type)) {
            // 统计近12个月
            result = getMonthlyTrendStatistics();
        } else if ("year".equals(type)) {
            // 统计近5年
            result = getYearlyTrendStatistics();
        }

        return result;
    }

    /**
     * 获取近12个月的事件趋势统计
     */
    private List<EventOccurTrendStatisticsVO> getMonthlyTrendStatistics() {
        List<EventOccurTrendStatisticsVO> result = new ArrayList<>();
        LocalDate now = LocalDate.now();

        for (int i = 11; i >= 0; i--) {
            LocalDate targetMonth = now.minusMonths(i);
            LocalDate firstDayOfMonth = targetMonth.withDayOfMonth(1);
            LocalDate lastDayOfMonth = targetMonth.withDayOfMonth(targetMonth.lengthOfMonth());

            LocalDateTime startTime = firstDayOfMonth.atStartOfDay();
            LocalDateTime endTime = lastDayOfMonth.atTime(LocalTime.MAX);

            // 统计各事件大类数量
            Long trafficCount = countEventsByCategory(EventCategory.TRAFFIC, startTime, endTime);
            Long driveCount = countEventsByCategory(EventCategory.DRIVE, startTime, endTime);
            Long passCount = countEventsByCategory(EventCategory.PASS, startTime, endTime);

            String period = targetMonth.getMonthValue() + "月";

            EventOccurTrendStatisticsVO vo = EventOccurTrendStatisticsVO.builder()
                    .period(period)
                    .trafficEventCount(trafficCount)
                    .driveEventCount(driveCount)
                    .passEventCount(passCount)
                    .build();

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取近5年的事件趋势统计
     */
    private List<EventOccurTrendStatisticsVO> getYearlyTrendStatistics() {
        List<EventOccurTrendStatisticsVO> result = new ArrayList<>();
        LocalDate now = LocalDate.now();

        // 从4年前开始到今年，共5年
        for (int i = 4; i >= 0; i--) {
            LocalDate targetYear = now.minusYears(i);
            LocalDate firstDayOfYear = targetYear.withDayOfYear(1);
            LocalDate lastDayOfYear = targetYear.withDayOfYear(targetYear.lengthOfYear());

            LocalDateTime startTime = firstDayOfYear.atStartOfDay();
            LocalDateTime endTime = lastDayOfYear.atTime(LocalTime.MAX);

            // 统计各事件大类数量
            Long trafficCount = countEventsByCategory(EventCategory.TRAFFIC, startTime, endTime);
            Long driveCount = countEventsByCategory(EventCategory.DRIVE, startTime, endTime);
            Long passCount = countEventsByCategory(EventCategory.PASS, startTime, endTime);

            String period = String.valueOf(targetYear.getYear());

            EventOccurTrendStatisticsVO vo = EventOccurTrendStatisticsVO.builder()
                    .period(period)
                    .trafficEventCount(trafficCount)
                    .driveEventCount(driveCount)
                    .passEventCount(passCount)
                    .build();

            result.add(vo);
        }

        return result;
    }

    /**
     * 根据事件大类统计指定时间范围内的事件数量
     */
    private Long countEventsByCategory(EventCategory category, LocalDateTime startTime, LocalDateTime endTime) {
        switch (category) {
            case TRAFFIC:
                return countTrafficEvents(startTime, endTime);
            case DRIVE:
                return countDriveEvents(startTime, endTime);
            case PASS:
                return countPassEvents(startTime, endTime);
            default:
                return 0L;
        }
    }

    /**
     * 获取拥堵易发路段统计
     *
     * @param type 统计类型：month（本月）、year（本年）
     * @return 拥堵易发路段统计数据（前5名）
     */
    public List<CongestionSegmentStatisticsVO> getCongestionSegmentStatistics(String type) {
        LocalDateTime startTime;
        LocalDateTime endTime;
        LocalDate now = LocalDate.now();

        // 根据类型确定时间范围
        if ("month".equals(type)) {
            // 统计本月
            LocalDate firstDayOfMonth = now.withDayOfMonth(1);
            startTime = firstDayOfMonth.atStartOfDay();
            endTime = now.withDayOfMonth(now.lengthOfMonth()).atTime(LocalTime.MAX);
        } else if ("year".equals(type)) {
            // 统计本年
            LocalDate firstDayOfYear = now.withDayOfYear(1);
            startTime = firstDayOfYear.atStartOfDay();
            endTime = now.withDayOfYear(now.lengthOfYear()).atTime(LocalTime.MAX);
        } else {
            return new ArrayList<>();
        }

        log.info("统计拥堵易发路段数据，时间范围：{} - {}", startTime, endTime);

        // 查询指定时间范围内的拥堵事件（主线拥堵和支线拥堵）
        List<TrafficEvent> congestionEvents = getCongestionEvents(startTime, endTime);

        if (congestionEvents.isEmpty()) {
            return new ArrayList<>();
        }

        // 按路段ID分组统计事件数量
        Map<Integer, Long> segmentEventCountMap = congestionEvents.stream()
                .filter(event -> event.getSegmentId() != null)
                .collect(Collectors.groupingBy(
                        TrafficEvent::getSegmentId,
                        Collectors.counting()
                ));

        // 计算总的拥堵事件数量
        long totalCongestionEvents = congestionEvents.size();

        // 转换为VO对象并计算占比
        List<CongestionSegmentStatisticsVO> result = segmentEventCountMap.entrySet().stream()
                .map(entry -> {
                    Integer segmentId = entry.getKey();
                    Long eventCount = entry.getValue();

                    // 获取路段名称
                    String segmentName = roadService.getRoadSegmentNameById(segmentId);

                    // 计算占比（百分比，不保留小数）
                    BigDecimal ratio = BigDecimal.valueOf(eventCount)
                            .divide(BigDecimal.valueOf(totalCongestionEvents), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(0, RoundingMode.HALF_UP);

                    return CongestionSegmentStatisticsVO.builder()
                            .segmentName(segmentName)
                            .congestionRatio(ratio)
                            .build();
                })
                .sorted((a, b) -> b.getCongestionRatio().compareTo(a.getCongestionRatio())) // 按占比降序排列
                .limit(5) // 只取前5名
                .collect(Collectors.toList());

        return result;
    }

    /**
     * 获取指定时间范围内的拥堵事件（主线拥堵和支线拥堵）
     */
    private List<TrafficEvent> getCongestionEvents(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<TrafficEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TrafficEvent::getEventType, EventType.CONGESTION_MAIN, EventType.CONGESTION_SLAVE)
                .ge(TrafficEvent::getStartTime, startTime)
                .le(TrafficEvent::getStartTime, endTime);
        return trafficEventMapper.selectList(wrapper);
    }

    /**
     * 获取违规驾驶类事件占比统计
     *
     * @param type 统计类型：month（本月）、year（本年）
     * @return 违规驾驶类事件占比统计数据
     */
    public List<DriveEventRatioStatisticsVO> getDriveEventRatioStatistics(String type) {
        LocalDateTime startTime;
        LocalDateTime endTime;
        LocalDate now = LocalDate.now();

        // 根据类型确定时间范围
        if ("month".equals(type)) {
            // 统计本月
            LocalDate firstDayOfMonth = now.withDayOfMonth(1);
            startTime = firstDayOfMonth.atStartOfDay();
            endTime = now.withDayOfMonth(now.lengthOfMonth()).atTime(LocalTime.MAX);
        } else if ("year".equals(type)) {
            // 统计本年
            LocalDate firstDayOfYear = now.withDayOfYear(1);
            startTime = firstDayOfYear.atStartOfDay();
            endTime = now.withDayOfYear(now.lengthOfYear()).atTime(LocalTime.MAX);
        } else {
            return new ArrayList<>();
        }

        log.info("统计违规驾驶类事件占比数据，时间范围：{} - {}", startTime, endTime);

        // 获取所有违规驾驶类事件类型

        List<EventType> driveEventTypes =  EventType.getAllDriveEvent();

        // 查询指定时间范围内的违规驾驶类事件
        List<DriveEvent> driveEvents = getDriveEventsByTimeRange(startTime, endTime);

        if (driveEvents.isEmpty()) {
            // 如果没有事件，返回占比为0的结果
            return driveEventTypes.stream()
                    .map(eventType -> DriveEventRatioStatisticsVO.builder()
                            .eventType(eventType)
                            .eventCount(0L)
                            .ratio(BigDecimal.ZERO)
                            .build())
                    .collect(Collectors.toList());
        }

        // 按事件类型分组统计数量
        Map<EventType, Long> eventTypeCountMap = driveEvents.stream()
                .collect(Collectors.groupingBy(
                        DriveEvent::getEventType,
                        Collectors.counting()
                ));

        // 计算总的违规驾驶类事件数量
        long totalDriveEvents = driveEvents.size();

        // 转换为VO对象并计算占比
        List<DriveEventRatioStatisticsVO> result = driveEventTypes.stream()
                .map(eventType -> {
                    Long eventCount = eventTypeCountMap.getOrDefault(eventType, 0L);

                    // 计算占比（百分比，保留2位小数）
                    BigDecimal ratio = BigDecimal.ZERO;

                    ratio = BigDecimal.valueOf(eventCount)
                            .divide(BigDecimal.valueOf(totalDriveEvents), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);

                    return DriveEventRatioStatisticsVO.builder()
                            .eventType(eventType)
                            .eventCount(eventCount)
                            .ratio(ratio)
                            .build();
                })
                .collect(Collectors.toList());

        return result;
    }

    /**
     * 获取指定时间范围内的违规驾驶类事件
     */
    private List<DriveEvent> getDriveEventsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<DriveEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(DriveEvent::getStartTime, startTime)
                .le(DriveEvent::getStartTime, endTime);
        return driveEventMapper.selectList(wrapper);
    }

    /**
     * 获取指定时间范围内的流量事件
     */
    private List<TrafficEvent> getTrafficEventsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<TrafficEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(TrafficEvent::getStartTime, startTime)
                .le(TrafficEvent::getStartTime, endTime);
        return trafficEventMapper.selectList(wrapper);
    }

    /**
     * 获取指定时间范围内的通行障碍事件
     */
    private List<PassEvent> getPassEventsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<PassEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(PassEvent::getStartTime, startTime)
                .le(PassEvent::getStartTime, endTime);
        return passEventMapper.selectList(wrapper);
    }

    /**
     * 获取按路段统计的驾驶异常事件数量（全部时间范围）
     *
     * @return 按路段统计的驾驶异常事件数量（包含事件数量为0的路段）
     */
    public List<DriveEventSegmentStatisticsVO> getDriveEventSegmentStatistics() {
        log.info("统计按路段的驾驶异常事件数据（全部时间范围）");

        // 获取所有路段信息
        List<Integer> allSegmentIds = roadService.getRoadSegments().stream()
                .map(RoadSegment::getId)
                .collect(Collectors.toList());
        RoadDirection[] allDirections = RoadDirection.values();

        // 查询全部的驾驶异常事件
        List<DriveEvent> driveEvents = driveEventMapper.selectList(null);

        // 按路段ID和方向分组统计事件数量
        Map<String, Long> segmentDirectionEventCountMap = driveEvents.stream()
                .filter(event -> event.getSegmentId() != null && event.getDirection() != null)
                .collect(Collectors.groupingBy(
                    event -> event.getSegmentId() + "_" + event.getDirection().name(),
                    Collectors.counting()
                ));

        // 为所有路段和方向组合生成统计结果
        List<DriveEventSegmentStatisticsVO> result = new ArrayList<>();

        for (Integer segmentId : allSegmentIds) {
            for (RoadDirection direction : allDirections) {
                String key = segmentId + "_" + direction.name();

                // 获取事件数量，如果没有事件则为0
                Long eventCount = segmentDirectionEventCountMap.getOrDefault(key, 0L);

                // 获取路段名称
                String segmentName = roadService.getRoadSegmentNameById(segmentId);

                DriveEventSegmentStatisticsVO vo = DriveEventSegmentStatisticsVO.builder()
                        .segmentName(segmentName)
                        .direction(direction)
                        .eventCount(eventCount)
                        .build();

                result.add(vo);
            }
        }

        return result;
    }

    /**
     * 获取近4周的拥堵里程占比趋势数据
     *
     * @return 拥堵里程占比趋势数据
     */
    public CongestionRatioTrendVO getCongestionMonthlyRatio() {
        LocalDate today = LocalDate.now();

        // 计算4周的时间范围（从4周前的周一开始到上周日结束）
        LocalDate endDate = today.minusDays(today.getDayOfWeek().getValue()); // 上周日
        LocalDate startDate = endDate.minusDays(27); // 4周前的周一

        List<List<BigDecimal>> data = new ArrayList<>();

        // 按周循环处理4周的数据
        for (int weekIndex = 0; weekIndex < 4; weekIndex++) {
            LocalDate weekStart = startDate.plusDays(weekIndex * 7);
            List<BigDecimal> weekData = new ArrayList<>();

            // 按天循环处理一周7天的数据
            for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
                LocalDate currentDate = weekStart.plusDays(dayIndex);
                LocalDateTime dayStart = currentDate.atStartOfDay();
                LocalDateTime dayEnd = currentDate.atTime(LocalTime.MAX);

                // 获取当天的拥堵占比（转换为0-1之间的小数）
                BigDecimal ratio = calculateDailyCongestionRatio(dayStart, dayEnd);
                weekData.add(ratio);
            }
            data.add(weekData);
        }

        return CongestionRatioTrendVO.builder()
                .data(data)
                .build();
    }

    /**
     * 计算指定日期的拥堵里程占比
     *
     * @param dayStart 当天开始时间
     * @param dayEnd   当天结束时间
     * @return 拥堵占比百分数
     */
    private BigDecimal calculateDailyCongestionRatio(LocalDateTime dayStart, LocalDateTime dayEnd) {
        // 查询当天的严重拥堵事件（level = 1）
        LambdaQueryWrapper<TrafficEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TrafficEvent::getEventType, EventType.CONGESTION_MAIN)
                .eq(TrafficEvent::getLevel, 1) // 严重拥堵
                .ge(TrafficEvent::getStartTime, dayStart)
                .le(TrafficEvent::getStartTime, dayEnd);

        List<TrafficEvent> congestionEvents = trafficEventMapper.selectList(wrapper);

        if (congestionEvents.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 找出当天最长的拥堵路段
        int maxCongestionLength = 0;
        Integer maxCongestionSegmentId = null;

        for (TrafficEvent event : congestionEvents) {
            int congestionLength = event.getEndMil() - event.getStartMil();
            if (congestionLength > maxCongestionLength) {
                maxCongestionLength = congestionLength;
            }
        }

        if (maxCongestionLength == 0) {
            return BigDecimal.ZERO;
        }

        // 获取路段全长
        List<Road> roadInfo = roadService.getRoadInfo();
        int totalRoadLength = Math.abs(roadInfo.get(0).getStartMil() - roadInfo.get(0).getEndMil());

        // 计算占比：拥堵长度 ÷ 道路全长 × 100%
        return BigDecimal.valueOf(maxCongestionLength)
                .divide(BigDecimal.valueOf(totalRoadLength), 3, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    /**
     * 当天事件数量
     *
     * @param segmentId 路段id
     * @return 事件数量
     */
    public List<EventCountVO> getDailyEventCount(Integer segmentId) {
        LocalDateTime start = LocalDate.now().atStartOfDay();
        List<EventCountVO> driveCount = driveEventMapper.countByEventType(segmentId, start, null);
        List<EventCountVO> passCount = passEventMapper.countByEventType(segmentId, start, null);
        List<EventCountVO> trafficCount = trafficEventMapper.countByEventType(segmentId, start, null);
        List<EventCountVO> list = new ArrayList<>();
        list.addAll(driveCount);
        list.addAll(passCount);
        list.addAll(trafficCount);
        return list;
    }
}
