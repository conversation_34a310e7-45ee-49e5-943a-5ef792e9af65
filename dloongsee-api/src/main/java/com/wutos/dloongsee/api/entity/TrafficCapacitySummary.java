package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.RoadDirection;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * <p>
 * 通行能力汇总
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Data
@TableName(value = "traffic_capacity_summary")
public class TrafficCapacitySummary {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 车流量
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * 当量交通量
     */
    @TableField(value = "ve")
    private Integer ve;

    /**
     * 平均行程时间(s)
     */
    @TableField(value = "time_avg")
    private Double timeAvg;

    /**
     * 平均行程速度
     */
    @TableField(value = "speed_avg")
    private Double speedAvg;

    /**
     * vc比
     */
    @TableField(value = "vc")
    private Double vc;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}