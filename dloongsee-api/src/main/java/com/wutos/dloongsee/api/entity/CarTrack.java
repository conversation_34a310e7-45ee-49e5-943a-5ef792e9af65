package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.CarType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "car_track")
public class CarTrack {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 车辆ID
     */
    private String carId;

    /**
     * 车牌号
     */
    private String carNum;

    /**
     * 车型
     */
    private CarType carType;

    /**
     * 方向
     */
    private RoadDirection direction;

    /**
     * 车重
     */
    private Double carWeight;

    /**
     * 平均速度
     */
    private Double speedAvg;

    /**
     * 最大速度
     */
    private Double speedMax;

    /**
     * 最小速度
     */
    private Double speedMin;

    /**
     * 进入点
     */
    private Integer startPoint;

    /**
     * 退出点
     */
    private Integer endPoint;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime = LocalDateTime.now();

}
