package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.api.entity.RoadRamp;
import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.utils.LnglatUtils;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 匝道
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
public class RoadRampVO {
    private Integer id;
    private String name;
    private RoadDirection direction;
    private RampType type;
    private Integer mil;
    private List<Lnglat> lnglats;

    public static RoadRampVO fromEntity(RoadRamp roadRamp) {
        RoadRampVO vo = new RoadRampVO();
        vo.setId(roadRamp.getId());
        vo.setName(roadRamp.getName());
        vo.setDirection(roadRamp.getDirection());
        vo.setType(roadRamp.getType());
        vo.setMil(roadRamp.getMil());
        vo.setLnglats(LnglatUtils.parse(roadRamp.getLnglats()));
        return vo;
    }
}
