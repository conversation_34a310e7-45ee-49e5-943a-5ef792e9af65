package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.common.enums.TrafficCapacityLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TrafficInfoVO {

    // 今日交通量
    private Integer todayTotalTrafficFlow;

    // 通行能力等级
    private TrafficCapacityLevel trafficCapacityLevel;

    // 通行能力等级中文
    private String trafficCapacityLevelLabel;

    // 拥堵指数(拥堵等级)
    private Integer congestionIndex;

    // 上行行程时间
    private Integer upTravelTime;

    // 下行行程时间
    private Integer downTravelTime;
}
