package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.service.GlobalEventService;
import com.wutos.dloongsee.api.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/global/event")
public class GlobalEventController {

    @Autowired
    private GlobalEventService globalEventService;

    @GetMapping("/getEventDataSummaryByDay")
    public TrafficEventSummaryVO getEventDataSummaryByDay() {
        return globalEventService.getEventDataSummaryByDay();
    }

    @GetMapping("/getEventTypeAnalysisByDay")
    public List<TrafficEventTypeAnalysisVO> getEventTypeAnalysisByDay() {
        return globalEventService.getEventTypeAnalysisByDay();
    }

    @GetMapping("/getHighEventSegmentByDay")
    public List<HighEventSegmentVO> getHighEventSegmentByDay() {
        return globalEventService.getHighEventSegmentByDay();
    }

    @GetMapping("/getHighEventTimeByDay")
    public List<HighEventTimeVO> getHighEventTimeByDay() {
        return globalEventService.getHighEventTimeByDay();
    }

    @GetMapping("/getEventStatisticsByDay")
    public List<EventStatisticsVO> getEventStatisticsByDay(@RequestParam Integer roadSegmentId) {
        return globalEventService.getEventStatisticsByDay(roadSegmentId);
    }

    @GetMapping("/getEventSpaceTimeStatisticsByDay")
    public List<EventSpaceTimeStatisticsVO> getEventSpaceTimeStatisticsByDay() {
        return globalEventService.getEventSpaceTimeStatisticsByDay();
    }
}
