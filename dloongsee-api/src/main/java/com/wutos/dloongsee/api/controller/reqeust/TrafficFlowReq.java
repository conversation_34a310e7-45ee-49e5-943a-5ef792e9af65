package com.wutos.dloongsee.api.controller.reqeust;

import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 交通流数据查询
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Data
public class TrafficFlowReq {
    private Integer pageNum;
    private Integer pageSize;
    private Integer segmentId;
    private RoadDirection direction;
    private LocalDate startTime;
    private LocalDate endTime;
    /**
     * 时间粒度
     * 1:5分钟 2:15分钟 3:30分钟 4:1小时 5:1天
     */
    private Integer timeGranularity;
}
