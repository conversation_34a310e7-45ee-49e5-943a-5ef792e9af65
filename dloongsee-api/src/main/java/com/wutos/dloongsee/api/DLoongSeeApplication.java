package com.wutos.dloongsee.api;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@SpringBootApplication(scanBasePackages = "com.wutos.dloongsee")
@EnableScheduling
@EnableAsync(proxyTargetClass = true)
@EnableWebSocket
@MapperScan(basePackages = "com.wutos.dloongsee.api.mapper")
public class DLoongSeeApplication {

    public static void main(String[] args) {
        SpringApplication.run(DLoongSeeApplication.class, args);
    }
}
