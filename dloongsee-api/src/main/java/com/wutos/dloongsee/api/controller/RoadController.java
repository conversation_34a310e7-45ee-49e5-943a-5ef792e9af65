package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.entity.Road;
import com.wutos.dloongsee.api.service.RoadService;
import com.wutos.dloongsee.api.vo.RoadSegmentDefineVO;
import com.wutos.dloongsee.api.vo.RoadSegmentVO;
import com.wutos.dloongsee.api.vo.RoadStructureVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 道路
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/15
 */
@RestController
@RequestMapping("/road")
public class RoadController {
    @Autowired
    private RoadService roadService;

    /**
     * 查询路段定义
     *
     * @param id 路段id
     * @return 路段定义
     */
    @GetMapping("/segment")
    public RoadSegmentDefineVO getRoadSegmentDefine(Integer id) {
        return roadService.getRoadSegmentDefine(id);
    }

    /**
     * 获取全部道路分段信息(gis 地图用)
     * @return 所有路段定义
     */
    @GetMapping("/segment/all")
    public List<RoadSegmentDefineVO> getAllRoadSegmentDefine() {
        return roadService.getAllRoadSegmentDefine();
    }

    /**
     * 获取全部道路分段字典数据
     * @return
     */
    @GetMapping("/segment/dict/all")
    public List<RoadSegmentVO> getAllRoadSegmentDict() {
        return roadService.getAllRoadSegmentDict();
    }


    /**
     * 获取全域路段所有 poi 数据
     * @return 所有基础设施(poi)数据
     */
    @GetMapping("/structure/all")
    public List<RoadStructureVO>  getAllStructure() {
        return roadService.getAllStructure();
    }

    /**
     * 获取全域道路基本信息
     * @return 所有道路基本信息
     */
    @GetMapping("/roadInfo")
    public List<Road>  getRoadInfo() {
        return roadService.getRoadInfo();
    }
}
