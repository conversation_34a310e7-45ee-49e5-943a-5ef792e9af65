package com.wutos.dloongsee.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wutos.dloongsee.api.entity.TrafficCapacity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ${description}
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
public interface TrafficCapacityMapper extends BaseMapper<TrafficCapacity> {
    void insertBatch(@Param("list") List<TrafficCapacity> list);

    List<Map<String, Object>> getAvgTravelTimeBar(@Param("date") LocalDateTime date);
}