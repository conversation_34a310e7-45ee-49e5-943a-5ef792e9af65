package com.wutos.dloongsee.api.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wutos.dloongsee.api.config.TrafficCapacityConfig;
import com.wutos.dloongsee.api.entity.TrafficCapacity;
import com.wutos.dloongsee.api.entity.TrafficCapacitySegment;
import com.wutos.dloongsee.api.mapper.TrafficCapacityMapper;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySegmentMapper;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelSpeedLineVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficAvgTravelTimeBarVO;
import com.wutos.dloongsee.api.vo.GlobalTrafficFlowAnalysisVO;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.utils.TaskTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GlobalTrafficService {

    @Autowired
    private TrafficCapacityMapper trafficCapacityMapper;

    @Autowired
    private TrafficCapacitySegmentMapper trafficCapacitySegmentMapper;

    @Autowired
    private TrafficCapacityConfig config;

    // 全域中中间部分的路段开始和结束里程（用于计算全域交通流量）
    private static final int TRAFFIC_CAPACITY_START_MIL = 1092242;

    private static final int TRAFFIC_CAPACITY_END_MIL = 1092742;

    public GlobalTrafficFlowAnalysisVO getTrafficFlowAnalysis() {
        LambdaQueryWrapper<TrafficCapacity> upQueryWrapper = new LambdaQueryWrapper<>();
        upQueryWrapper.eq(TrafficCapacity::getStartMil, TRAFFIC_CAPACITY_START_MIL)
                .eq(TrafficCapacity::getEndMil, TRAFFIC_CAPACITY_END_MIL)
                .eq(TrafficCapacity::getDirection, RoadDirection.UP)
                .orderByDesc(TrafficCapacity::getCreateTime)
                .last("limit 10");
        List<TrafficCapacity> upTrafficCapacities = trafficCapacityMapper.selectList(upQueryWrapper);

        LambdaQueryWrapper<TrafficCapacity> downQueryWrapper = new LambdaQueryWrapper<>();
        downQueryWrapper.eq(TrafficCapacity::getStartMil, TRAFFIC_CAPACITY_START_MIL)
                .eq(TrafficCapacity::getEndMil, TRAFFIC_CAPACITY_END_MIL)
                .eq(TrafficCapacity::getDirection, RoadDirection.DOWN)
                .orderByDesc(TrafficCapacity::getCreateTime)
                .last("limit 10");
        List<TrafficCapacity> downTrafficCapacities = trafficCapacityMapper.selectList(downQueryWrapper);

        List<String> times = upTrafficCapacities.stream().map(capacity -> DateUtil.format(capacity.getCreateTime(), "HH:mm"))
                .collect(Collectors.toList());
        List<Integer> upCarFlow = upTrafficCapacities.stream().map(TrafficCapacity::getNum).collect(Collectors.toList());
        List<Integer> downCarFlow = downTrafficCapacities.stream().map(TrafficCapacity::getNum).collect(Collectors.toList());

        return GlobalTrafficFlowAnalysisVO.builder()
                .times(times)
                .upCarFlow(upCarFlow)
                .downCarFlow(downCarFlow)
                .build();
    }

    public GlobalTrafficAvgTravelSpeedLineVO getAvgTravelSpeedLine() {
        LambdaQueryWrapper<TrafficCapacitySegment> segmentQueryWrapper = new LambdaQueryWrapper<>();
        segmentQueryWrapper.orderByAsc(TrafficCapacitySegment::getStartMil);
        List<Integer> milPoints = trafficCapacitySegmentMapper.selectList(segmentQueryWrapper)
                .stream().map(TrafficCapacitySegment::getStartMil).collect(Collectors.toList());

        //获取最近一次计算的时间，来获取上下行速度行程曲线
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());

        LambdaQueryWrapper<TrafficCapacity> upQueryWrapper = new LambdaQueryWrapper<>();
        upQueryWrapper.eq(TrafficCapacity::getDirection, RoadDirection.UP)
                .eq(TrafficCapacity::getCreateTime, lastExecutionTime)
                .orderByAsc(TrafficCapacity::getStartMil);
        List<Double> upSpeeds = trafficCapacityMapper.selectList(upQueryWrapper)
                .stream().map(capacity -> capacity.getSpeedAvg() * 3.6).collect(Collectors.toList());

        LambdaQueryWrapper<TrafficCapacity> downQueryWrapper = new LambdaQueryWrapper<>();
        downQueryWrapper.eq(TrafficCapacity::getDirection, RoadDirection.DOWN)
                .eq(TrafficCapacity::getCreateTime, lastExecutionTime)
                .orderByAsc(TrafficCapacity::getStartMil);
        List<Double> downSpeeds = trafficCapacityMapper.selectList(downQueryWrapper)
                .stream().map(capacity -> capacity.getSpeedAvg() * 3.6).collect(Collectors.toList());

        return GlobalTrafficAvgTravelSpeedLineVO.builder()
                .milPoints(milPoints)
                .upSpeeds(upSpeeds)
                .downSpeeds(downSpeeds)
                .build();
    }


    public GlobalTrafficAvgTravelTimeBarVO getAvgTravelTimeBar() {
        //获取最近一次计算的时间，来获取上下行行程时间
        LocalDateTime lastExecutionTime = TaskTimeUtils.getLastExecutionTime(LocalDateTime.now(), config.getDuration());

        List<Map<String, Object>> result = trafficCapacityMapper.getAvgTravelTimeBar(lastExecutionTime);

        List<String> roadSegments = new ArrayList<>();
        List<Double> upTimes = new ArrayList<>();
        List<Double> downTimes = new ArrayList<>();

        for (Map<String, Object> row : result) {
            roadSegments.add((String) row.get("roadSegment"));
            upTimes.add(row.get("upTimeAvg") == null ? 0.0 : ((Number) row.get("upTimeAvg")).doubleValue() / 60);
            downTimes.add(row.get("downTimeAvg") == null ? 0.0 : ((Number) row.get("downTimeAvg")).doubleValue() / 60);
        }

        return GlobalTrafficAvgTravelTimeBarVO.builder()
                .roadSegments(roadSegments)
                .upTimes(upTimes)
                .downTimes(downTimes)
                .build();
    }

}
