package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.service.EventStatisticsService;
import com.wutos.dloongsee.api.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 事件统计控制器
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@RestController
@RequestMapping("/event/statistics")
public class EventStatisticsController {

    @Autowired
    private EventStatisticsService eventStatisticsService;

    /**
     * 获取事件总体情况统计
     *
     * @param type 统计类型：day（当天）、month（当月）、year（当年）
     * @return 事件统计结果
     */
    @GetMapping("/overview")
    public BaseResponse<EventTotalStatisticsVO> getEventStatistics(
            @RequestParam(value = "type", required = false, defaultValue = "day") String type) {

        if (!"day".equals(type) && !"month".equals(type) && !"year".equals(type)) {
            return BaseResponse.error("type参数值只能是day、month、year中的一个");
        }

        EventTotalStatisticsVO statistics = eventStatisticsService.getEventStatistics(type);
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取近7日高发事件统计
     *
     * @return 近7日每天各类事件的统计数据
     */
    @GetMapping("/recentSevenDays")
    public BaseResponse<List<SevenDayEventDateStatisticsVO>> getRecentSevenDaysEventStats() {
        List<SevenDayEventDateStatisticsVO> statistics = eventStatisticsService.getRecentSevenDaysEventStats();
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取事件发生趋势统计
     *
     * @param type 统计类型：month（近12个月）、year（近5年）
     * @return 事件发生趋势统计数据
     */
    @GetMapping("/eventOccurTrend")
    public BaseResponse<List<EventOccurTrendStatisticsVO>> getEventOccurTrendStatistics(
            @RequestParam("type") String type) {

        if (!"month".equals(type) && !"year".equals(type)) {
            return BaseResponse.error("type参数值只能是month、year中的一个");
        }

        List<EventOccurTrendStatisticsVO> statistics = eventStatisticsService.getEventOccurTrendStatistics(type);
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取拥堵易发路段统计
     *
     * @param type 统计类型：month（本月）、year（本年）
     * @return 拥堵易发路段统计数据（前5名）
     */
    @GetMapping("/congestionSegments")
    public BaseResponse<List<CongestionSegmentStatisticsVO>> getCongestionSegmentStatistics(
            @RequestParam("type") String type) {

        // 验证参数值
        if (!"month".equals(type) && !"year".equals(type)) {
            return BaseResponse.error("type参数值只能是month、year中的一个");
        }

        List<CongestionSegmentStatisticsVO> statistics = eventStatisticsService.getCongestionSegmentStatistics(type);
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取违规驾驶类事件占比统计
     *
     * @param type 统计类型：month（本月）、year（本年）
     * @return 违规驾驶类事件占比统计数据
     */
    @GetMapping("/driveEventRatio")
    public BaseResponse<List<DriveEventRatioStatisticsVO>> getDriveEventRatioStatistics(
            @RequestParam("type") String type) {

        // 验证参数值
        if (!"month".equals(type) && !"year".equals(type)) {
            return BaseResponse.error("type参数值只能是month、year中的一个");
        }

        List<DriveEventRatioStatisticsVO> statistics = eventStatisticsService.getDriveEventRatioStatistics(type);
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取按路段统计的驾驶异常事件数量（全部时间范围）
     *
     * @return 按路段统计的驾驶异常事件数量
     */
    @GetMapping("/driveEventSegment")
    public BaseResponse<List<DriveEventSegmentStatisticsVO>> getDriveEventSegmentStatistics() {
        List<DriveEventSegmentStatisticsVO> statistics = eventStatisticsService.getDriveEventSegmentStatistics();
        return BaseResponse.ok(statistics);
    }

    /**
     * 获取近4周的拥堵里程占比数据
     *
     * @return 拥堵里程占比趋势数据
     */
    @GetMapping("/congestionMonthlyRatio")
    public BaseResponse<CongestionRatioTrendVO> getCongestionMonthlyRatio() {
        CongestionRatioTrendVO statistics = eventStatisticsService.getCongestionMonthlyRatio();
        return BaseResponse.ok(statistics);
    }

    /**
     * 当天事件数量
     *
     * @param segmentId 路段id
     * @return 事件数量
     */
    @GetMapping("/getDailyEventCount")
    public List<EventCountVO> getDailyEventCount(@RequestParam(required = false) Integer segmentId) {
        return eventStatisticsService.getDailyEventCount(segmentId);
    }
}
