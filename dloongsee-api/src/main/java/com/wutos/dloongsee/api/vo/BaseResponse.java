package com.wutos.dloongsee.api.vo;

import lombok.Data;

/**
 * <p>
 * 基础响应
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/15
 */
@Data
public class BaseResponse<T> {
    /**
     * 响应码, 0: 正常, 否则有异常, 异常信息见msg
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 业务成功, 封装响应实体数据
     */
    private T data;

    /**
     * 成功返回，无数据
     */
    public static BaseResponse<Void> ok() {
        return ok(null);
    }

    /**
     * 成功返回，有数据
     */
    public static <T> BaseResponse<T> ok(T data) {
        BaseResponse<T> res = new BaseResponse<>();
        res.setCode(0);
        res.setMsg("success");
        res.setData(data);
        return res;
    }

    /**
     * 失败返回
     */
    public static <T> BaseResponse<T> error(String msg) {
        BaseResponse<T> res = new BaseResponse<>();
        res.setCode(500);
        res.setMsg(msg);
        return res;
    }
}
