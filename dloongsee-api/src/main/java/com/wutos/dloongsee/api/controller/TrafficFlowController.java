package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.controller.reqeust.TrafficFlowReq;
import com.wutos.dloongsee.api.service.TrafficFlowService;
import com.wutos.dloongsee.api.vo.PageResponse;
import com.wutos.dloongsee.api.vo.TrafficFlowVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 交通流参数分析
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@RestController
@RequestMapping("/trafficFlow")
public class TrafficFlowController {
    @Autowired
    private TrafficFlowService trafficFlowService;

    /**
     * 交通流分页查询
     *
     * @param req 请求
     * @return 结果
     */
    @PostMapping("/page")
    public PageResponse<TrafficFlowVO> pageTrafficFlows(@RequestBody TrafficFlowReq req) {
        return trafficFlowService.pageTrafficFlows(req);
    }

    /**
     * 查询当天每小时的交通流数据
     *
     * @param segmentId 路段id
     * @return 当天每小时的交通流数据
     */
    @GetMapping("/hourly")
    public List<TrafficFlowVO> getTodayHourlyTrafficFlows(@RequestParam(value = "segmentId", required = false) Integer segmentId) {
        return trafficFlowService.getTodayHourlyTrafficFlows(segmentId);
    }
}
