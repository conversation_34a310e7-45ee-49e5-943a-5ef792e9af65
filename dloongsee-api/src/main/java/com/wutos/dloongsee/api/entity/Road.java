package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

/**
 * <p>
 * 道路表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
@TableName(value = "road")
public class Road {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名字
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 车道数
     */
    @TableField(value = "lane_count")
    private Integer laneCount;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 经纬度数据
     */
    @TableField(value = "lnglats")
    private String lnglats;
}