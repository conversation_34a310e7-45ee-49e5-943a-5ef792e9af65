package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * 路段表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
@TableName(value = "road_segment")
public class RoadSegment {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名字
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 车道数
     */
    @TableField(value = "lane_count")
    private Integer laneCount;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 经纬度数据
     */
    @TableField(value = "lnglats")
    private String lnglats;

    /**
     * 是否有应急车道
     */
    @TableField(value = "has_emergency_lane")
    private Byte hasEmergencyLane;
}