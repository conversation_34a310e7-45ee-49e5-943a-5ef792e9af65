package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.service.TrafficService;
import com.wutos.dloongsee.api.vo.TrafficInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 交通态势
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/28
 */
@RestController
@RequestMapping("/traffic")
public class TrafficController {
    @Autowired
    private TrafficService trafficService;

    /**
     * 交通流信息(通行能力、拥堵指数、行程时间)
     * @return TrafficInfoVO
     */
    @GetMapping("/trafficInfo")
    public TrafficInfoVO getTrafficInfo(@RequestParam("segmentId") Integer segmentId) {
        return trafficService.getTrafficInfo(segmentId);
    }
}
