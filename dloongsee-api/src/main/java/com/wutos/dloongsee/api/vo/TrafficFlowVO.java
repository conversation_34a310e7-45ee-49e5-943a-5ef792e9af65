package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.TrafficCapacityLevel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 交通流数据
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Data
public class TrafficFlowVO {
    private LocalDateTime time;
    private Integer segmentId;
    private String segmentName;
    private RoadDirection direction;
    private Integer startMil;
    private Integer endMil;
    private Integer num;
    private Integer ve;
    private Double timeAvg;
    private Double speedAvg;
    private Double vc;
    private TrafficCapacityLevel capacityLevel;
    private Integer congestionLevel;
}
