package com.wutos.dloongsee.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wutos.dloongsee.api.entity.TrafficCapacitySummary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通行能力汇总
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
public interface TrafficCapacitySummaryMapper extends BaseMapper<TrafficCapacitySummary> {
    void insertBatch(@Param("list") List<TrafficCapacitySummary> list);
}