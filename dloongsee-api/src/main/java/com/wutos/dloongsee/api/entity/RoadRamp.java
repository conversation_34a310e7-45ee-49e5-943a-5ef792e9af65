package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

/**
 * <p>
 * 道路匝道表
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
@TableName(value = "road_ramp")
public class RoadRamp {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名字
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 方向
     */
    @TableField(value = "direction")
    private RoadDirection direction;

    /**
     * 进/出
     */
    @TableField(value = "`type`")
    private RampType type;

    /**
     * 里程
     */
    @TableField(value = "mil")
    private Integer mil;

    /**
     * 经纬度数据
     */
    @TableField(value = "lnglats")
    private String lnglats;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;
}