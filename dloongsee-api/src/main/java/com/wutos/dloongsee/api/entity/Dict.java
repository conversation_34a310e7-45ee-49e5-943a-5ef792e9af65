package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "dict")
public class Dict {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 字典类型编码（如 direction, event_type, bridge_type）
     */
    private String typeCode;

    /**
     * 字典类型名称（如 方向、事件类型、桥梁类型）
     */
    private String typeLabel;

    /**
     * 字典项编码（如 overspeed, wrong_direction）
     */
    private String itemCode;

    /**
     * 字典项值（显示给用户的值，如 超速、逆行）
     */
    private String itemValue;

    /**
     * 排序号（越小越靠前）
     */
    private Integer sortOrder;

    /**
     * 状态（1 启用，0 停用）
     */
    private Byte status;

    /**
     * 描述
     */
    private String description;

}
