package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.common.enums.EventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 违规驾驶事件占比统计VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DriveEventRatioStatisticsVO {

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 事件数量
     */
    private Long eventCount;

    /**
     * 占比（百分比）
     */
    private BigDecimal ratio;
}
