package com.wutos.dloongsee.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 近7日事件统计VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SevenDayEventDateStatisticsVO {

    /**
     * 日期（格式：yyyy-MM-dd）
     */
    private String date;

    /**
     * 主线拥堵事件数量
     */
    @JsonProperty("CONGESTION_MAIN")
    private Long mainCongestionCount;

    /**
     * 支线拥堵事件数量
     */
    @JsonProperty("CONGESTION_SLAVE")
    private Long slaveCongestionCount;

    /**
     * 流量激增事件数量
     */
    @JsonProperty("TRAFFIC_SURGE")
    private Long trafficSurgeCount;

    /**
     * 速度差异常事件数量
     */
    @JsonProperty("SPEED_DIFF")
    private Long speedDiffCount;

    /**
     * 停车事件数量
     */
    @JsonProperty("STOP")
    private Long stopCount;

    /**
     * 超速事件数量
     */
    @JsonProperty("OVER_SPEED")
    private Long overSpeedCount;

    /**
     * 逆行事件数量
     */
    @JsonProperty("BACK_DRIVE")
    private Long backDriveCount;

    /**
     * 长期占用应急车道事件数量
     */
    @JsonProperty("EMERGENCY_LANE")
    private Long emergencyLaneCount;

    /**
     * 抛洒物事件数量
     */
    @JsonProperty("SPRINKLE")
    private Long sprinkleCount;

    /**
     * 行人入侵事件数量
     */
    @JsonProperty("PEDESTRIAN")
    private Long pedestrianCount;

    /**
     * 行车道占用事件
     */
    @JsonProperty("DRIVE_LANE_OCCUPY")
    private Long driveLaneOccupyCount;

    /**
     * 低速事件
     */
    @JsonProperty("LOW_SPEED")
    private Long lowSpeedCount;

}
