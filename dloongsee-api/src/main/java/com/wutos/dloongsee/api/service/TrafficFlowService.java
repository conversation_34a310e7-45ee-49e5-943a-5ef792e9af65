package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wutos.dloongsee.api.config.TrafficCapacityConfig;
import com.wutos.dloongsee.api.controller.reqeust.TrafficFlowReq;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficCapacitySummary;
import com.wutos.dloongsee.api.mapper.TrafficCapacitySummaryMapper;
import com.wutos.dloongsee.api.vo.PageResponse;
import com.wutos.dloongsee.api.vo.RoadSegmentVO;
import com.wutos.dloongsee.api.vo.TrafficFlowVO;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.TrafficCapacityLevel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 交通流参数分析
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Service
public class TrafficFlowService {

    @Autowired
    private TrafficCapacitySummaryMapper trafficCapacitySummaryMapper;
    @Autowired
    private RoadService roadService;
    @Autowired
    private TrafficCapacityService trafficCapacityService;
    @Autowired
    private TrafficCapacityConfig config;

    /**
     * 交通流分页查询
     *
     * @param req 请求
     * @return 结果
     */
    public PageResponse<TrafficFlowVO> pageTrafficFlows(TrafficFlowReq req) {
        List<TrafficFlowVO> result = getTrafficFlows(req.getSegmentId(), req.getDirection(), req.getStartTime().atStartOfDay(), req.getEndTime().atTime(LocalTime.MAX), req.getTimeGranularity());

        // 手动分页
        int fromIndex = (req.getPageNum() - 1) * req.getPageSize();
        int toIndex = Math.min(fromIndex + req.getPageSize(), result.size());
        List<TrafficFlowVO> records = fromIndex >= result.size() ? Collections.emptyList() : result.subList(fromIndex, toIndex);
        return new PageResponse<>(result.size(), req.getPageNum(), req.getPageSize(), records);
    }

    /**
     * 查询当天每小时的交通流数据
     *
     * @param segmentId 路段id
     * @return 当天每小时的交通流数据
     */
    public List<TrafficFlowVO> getTodayHourlyTrafficFlows(Integer segmentId) {
        LocalDate today = LocalDate.now();
        List<TrafficFlowVO> trafficFlows = getTrafficFlows(segmentId, null, today.minusDays(1).atTime(23, 0), today.atTime(23, 0), 4);
        // 按时间、路段分组
        Map<LocalDateTime, Map<Integer, List<TrafficFlowVO>>> flowMap = trafficFlows.stream().collect(
                Collectors.groupingBy(TrafficFlowVO::getTime,
                        Collectors.groupingBy(TrafficFlowVO::getSegmentId)));
        // 生成当天每小时时间点, 填充数据
        List<LocalDateTime> hours = IntStream.rangeClosed(0, 23).mapToObj(h -> today.atTime(h, 0)).collect(Collectors.toList());
        List<Integer> segmentIds = roadService.getAllRoadSegmentDict().stream().map(RoadSegmentVO::getValue)
                .filter(e -> segmentId == null || Objects.equals(e, segmentId)).collect(Collectors.toList());
        List<TrafficFlowVO> filledFlows = new ArrayList<>();
        for (LocalDateTime hour : hours) {
            for (Integer sid : segmentIds) {
                List<TrafficFlowVO> list = flowMap.getOrDefault(hour, Collections.emptyMap()).getOrDefault(sid, Collections.emptyList());
                if (list.isEmpty()) {
                    TrafficFlowVO up = new TrafficFlowVO();
                    up.setTime(hour);
                    up.setSegmentId(sid);
                    up.setSegmentName(roadService.getRoadSegmentNameById(sid));
                    up.setDirection(RoadDirection.UP);
                    up.setNum(0);
                    up.setSpeedAvg(config.getFreeSpeed() / 3.6);
                    up.setVe(0);
                    up.setVc(0d);
                    up.setCapacityLevel(TrafficCapacityLevel.A);
                    up.setCongestionLevel(4);
                    TrafficFlowVO down = new TrafficFlowVO();
                    down.setTime(hour);
                    down.setSegmentId(sid);
                    down.setSegmentName(roadService.getRoadSegmentNameById(sid));
                    down.setDirection(RoadDirection.DOWN);
                    down.setNum(0);
                    down.setSpeedAvg(config.getFreeSpeed() / 3.6);
                    down.setVe(0);
                    down.setVc(0d);
                    down.setCapacityLevel(TrafficCapacityLevel.A);
                    down.setCongestionLevel(4);
                    filledFlows.add(up);
                    filledFlows.add(down);
                } else {
                    filledFlows.addAll(list);
                }
            }
        }
        return filledFlows;
    }

    /**
     * 查询交通流
     *
     * @param segmentId       路段id
     * @param direction       方向
     * @param startTime       开始时间(不包含)
     * @param endTime         结束时间(包含)
     * @param timeGranularity 时间粒度
     * @return 交通流
     */
    private List<TrafficFlowVO> getTrafficFlows(Integer segmentId, RoadDirection direction, LocalDateTime startTime, LocalDateTime endTime, Integer timeGranularity) {
        List<TrafficCapacitySummary> summaries = trafficCapacitySummaryMapper.selectList(
                Wrappers.lambdaQuery(TrafficCapacitySummary.class)
                        .eq(segmentId != null, TrafficCapacitySummary::getSegmentId, segmentId)
                        .eq(direction != null, TrafficCapacitySummary::getDirection, direction)
                        .gt(TrafficCapacitySummary::getCreateTime, startTime)
                        .le(TrafficCapacitySummary::getCreateTime, endTime)
                        .orderByAsc(TrafficCapacitySummary::getCreateTime, TrafficCapacitySummary::getSegmentId)
        );

        // 先按时间粒度, 再按路段分组分组
        Map<LocalDateTime, Map<Integer, List<TrafficCapacitySummary>>> map = summaries.stream().collect(
                Collectors.groupingBy(e -> roundUpTime(e.getCreateTime(), timeGranularity), TreeMap::new,
                        Collectors.groupingBy(TrafficCapacitySummary::getSegmentId, TreeMap::new, Collectors.toList())));

        List<TrafficFlowVO> result = new ArrayList<>();
        for (LocalDateTime time : map.keySet()) {
            Map<Integer, List<TrafficCapacitySummary>> segmentMap = map.get(time);
            for (Integer sid : segmentMap.keySet()) {
                List<TrafficCapacitySummary> list = segmentMap.get(sid);
                // 分别计算两个方向
                List<TrafficCapacitySummary> up = list.stream().filter(e -> e.getDirection() == RoadDirection.UP).collect(Collectors.toList());
                List<TrafficCapacitySummary> down = list.stream().filter(e -> e.getDirection() == RoadDirection.DOWN).collect(Collectors.toList());
                if (!up.isEmpty()) {
                    result.add(toVO(up, RoadDirection.UP, sid, time));
                }
                if (!down.isEmpty()) {
                    result.add(toVO(down, RoadDirection.DOWN, sid, time));
                }
            }
        }
        return result;
    }

    /**
     * 时间窗口内单方向数据聚合
     */
    private TrafficFlowVO toVO(List<TrafficCapacitySummary> onewaySummaries, RoadDirection direction, Integer segmentId, LocalDateTime time) {
        int num = 0;
        int ve = 0;
        double t = 0;
        int length = 0;
        double vcs = 0;
        for (TrafficCapacitySummary summary : onewaySummaries) {
            num += summary.getNum();
            ve += summary.getVe();
            t += summary.getTimeAvg();
            length += summary.getEndMil() - summary.getStartMil();
            vcs += summary.getVc();
        }
        List<RoadSegment> roadSegments = roadService.getRoadSegments();
        int startMil = roadSegments.get(0).getStartMil();
        int endMil = roadSegments.get(roadSegments.size() - 1).getEndMil();
        int laneCount = roadSegments.get(0).getHasEmergencyLane() == 1 ? roadSegments.get(0).getLaneCount() - 1 : roadSegments.get(0).getLaneCount();
        Optional<RoadSegment> optional = roadSegments.stream().filter(e -> Objects.equals(e.getId(), segmentId)).findFirst();
        if (optional.isPresent()) {
            RoadSegment roadSegment = optional.get();
            startMil = roadSegment.getStartMil();
            endMil = roadSegment.getEndMil();
            laneCount = roadSegment.getHasEmergencyLane() == 1 ? roadSegment.getLaneCount() - 1 : roadSegment.getLaneCount();
        }
        double vc = vcs / onewaySummaries.size();
        // 拥堵指数
        double speedAvg = length / t;
        int congestionLevel = trafficCapacityService.getCongestionLevel(num, laneCount, speedAvg);
        TrafficFlowVO vo = new TrafficFlowVO();
        vo.setTime(time);
        vo.setSegmentId(segmentId);
        vo.setSegmentName(roadService.getRoadSegmentNameById(segmentId));
        vo.setDirection(direction);
        vo.setStartMil(startMil);
        vo.setEndMil(endMil);
        vo.setNum(num);
        vo.setVe(ve);
        vo.setTimeAvg(t / onewaySummaries.size());
        vo.setSpeedAvg(speedAvg);
        vo.setVc(vc);
        vo.setCapacityLevel(TrafficCapacityLevel.getLevel(vc));
        vo.setCongestionLevel(congestionLevel);
        return vo;
    }

    /**
     * 根据指定的粒度，将时间向上取整到对应的窗口结束时间
     */
    private static LocalDateTime roundUpTime(LocalDateTime time, Integer timeGranularity) {
        int minuteOfHour;
        int newMinute;
        LocalDateTime adjustedTime = time.truncatedTo(ChronoUnit.SECONDS).minusNanos(1);
        switch (timeGranularity) {
            case 1:
                // 5分钟
                minuteOfHour = adjustedTime.getMinute();
                newMinute = (minuteOfHour / 5) * 5;
                return adjustedTime.withMinute(newMinute).truncatedTo(ChronoUnit.MINUTES).plusMinutes(5);
            case 2:
                // 15分钟
                minuteOfHour = adjustedTime.getMinute();
                newMinute = (minuteOfHour / 15) * 15;
                return adjustedTime.withMinute(newMinute).truncatedTo(ChronoUnit.MINUTES).plusMinutes(15);
            case 3:
                // 30分钟
                minuteOfHour = adjustedTime.getMinute();
                newMinute = (minuteOfHour / 30) * 30;
                return adjustedTime.withMinute(newMinute).truncatedTo(ChronoUnit.MINUTES).plusMinutes(30);
            case 4:
                // 1小时
                return adjustedTime.truncatedTo(ChronoUnit.HOURS).plusHours(1);
            case 5:
                // 1天
                return adjustedTime.truncatedTo(ChronoUnit.DAYS).plusDays(1);
            default:
                throw new IllegalArgumentException("不支持的时间粒度: " + timeGranularity);
        }
    }
}
