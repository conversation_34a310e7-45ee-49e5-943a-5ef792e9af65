package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.api.entity.RoadRamp;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.RoadStructure;
import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.utils.LnglatUtils;
import com.wutos.dloongsee.common.utils.RoadDirectionUtils;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 路段定义
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
public class RoadSegmentDefineVO {
    private Integer id;
    private String name;
    private RoadSegmentDefineVO preSegment;
    private RoadSegmentDefineVO nextSegment;
    private Integer laneCount;
    private Integer startMil;
    private Integer endMil;
    private List<Lnglat> lnglats;
    private Boolean hasEmergencyLane;
    private Boolean milFromLeft;
    private List<RoadRampVO> ramps;
    private List<RoadStructureVO> structures;

    public static RoadSegmentDefineVO fromEntity(RoadSegment roadSegment, List<RoadRamp> ramps, List<RoadStructure> structures, RoadSegment pre, RoadSegment next) {
        RoadSegmentDefineVO preSegment = null;
        if (pre != null) {
            preSegment = new RoadSegmentDefineVO();
            preSegment.setId(pre.getId());
            preSegment.setName(pre.getName());
        }
        RoadSegmentDefineVO nextSegment = null;
        if (next != null) {
            nextSegment = new RoadSegmentDefineVO();
            nextSegment.setId(next.getId());
            nextSegment.setName(next.getName());
        }

        RoadSegmentDefineVO vo = new RoadSegmentDefineVO();
        vo.setId(roadSegment.getId());
        vo.setName(roadSegment.getName());
        vo.setPreSegment(preSegment);
        vo.setNextSegment(nextSegment);
        vo.setLaneCount(roadSegment.getLaneCount());
        vo.setStartMil(roadSegment.getStartMil());
        vo.setEndMil(roadSegment.getEndMil());
        vo.setLnglats(LnglatUtils.parse(roadSegment.getLnglats()));
        vo.setHasEmergencyLane(roadSegment.getHasEmergencyLane() == 1);
        vo.setMilFromLeft(RoadDirectionUtils.isMilFromLeft());
        vo.setRamps(ramps.stream().map(RoadRampVO::fromEntity).collect(Collectors.toList()));
        vo.setStructures(structures.stream().map(RoadStructureVO::fromEntity).collect(Collectors.toList()));
        return vo;
    }

    public static RoadSegmentDefineVO fromEntity(RoadSegment roadSegment) {
        RoadSegmentDefineVO vo = new RoadSegmentDefineVO();
        vo.setId(roadSegment.getId());
        vo.setName(roadSegment.getName());
        vo.setStartMil(roadSegment.getStartMil());
        vo.setEndMil(roadSegment.getEndMil());
        vo.setLnglats(LnglatUtils.parse(roadSegment.getLnglats()));
        return vo;
    }
}
