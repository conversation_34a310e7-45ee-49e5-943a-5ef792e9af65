package com.wutos.dloongsee.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 拥堵里程占比趋势统计VO
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CongestionRatioTrendVO {

    /**
     * 4周×7天的拥堵占比数据
     * 外层List表示4周，内层List表示每周7天的拥堵占比
     * 数据格式：[[week1_day1, week1_day2, ..., week1_day7], [week2_day1, ...], ...]
     */
    private List<List<BigDecimal>> data;
}
