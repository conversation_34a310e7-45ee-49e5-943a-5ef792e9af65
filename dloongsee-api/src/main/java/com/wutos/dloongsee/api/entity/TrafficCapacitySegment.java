package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * 通行能力分段
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/20
 */
@Data
@TableName(value = "traffic_capacity_segment")
public class TrafficCapacitySegment {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 开始里程
     */
    @TableField(value = "start_mil")
    private Integer startMil;

    /**
     * 结束里程
     */
    @TableField(value = "end_mil")
    private Integer endMil;

    /**
     * 路段id
     */
    @TableField(value = "segment_id")
    private Integer segmentId;
}