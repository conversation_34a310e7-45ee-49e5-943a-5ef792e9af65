package com.wutos.dloongsee.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wutos.dloongsee.api.entity.TrafficEvent;
import com.wutos.dloongsee.api.vo.EventCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 流量异常事件
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/21
 */
@Mapper
public interface TrafficEventMapper extends BaseMapper<TrafficEvent> {
    Integer updateEndTimeAndEndMil(@Param("endTime") LocalDateTime endTime, @Param("endMil") Integer endMil, @Param("eventId") String eventId);

    List<EventCountVO> countByEventType(@Param("segmentId") Integer segmentId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}