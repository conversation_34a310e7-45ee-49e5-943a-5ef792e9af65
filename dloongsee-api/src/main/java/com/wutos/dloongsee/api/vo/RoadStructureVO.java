package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.api.entity.RoadStructure;
import com.wutos.dloongsee.common.dto.Lnglat;
import com.wutos.dloongsee.common.enums.BridgeType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.enums.StructureType;
import com.wutos.dloongsee.common.utils.LnglatUtils;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 基础设施
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/14
 */
@Data
public class RoadStructureVO {
    private Integer id;
    private String name;
    private StructureType type;
    private BridgeType bridgeType;
    private RoadDirection direction;
    private Integer startMil;
    private Integer endMil;
    private List<Lnglat> lnglats;

    public static RoadStructureVO fromEntity(RoadStructure roadStructure) {
        RoadStructureVO vo = new RoadStructureVO();
        vo.setId(roadStructure.getId());
        vo.setName(roadStructure.getName());
        vo.setType(roadStructure.getType());
        vo.setBridgeType(roadStructure.getBridgeType());
        vo.setDirection(roadStructure.getDirection());
        vo.setStartMil(roadStructure.getStartMil());
        vo.setEndMil(roadStructure.getEndMil());
        vo.setLnglats(LnglatUtils.parse(roadStructure.getLnglats()));
        return vo;
    }
}
