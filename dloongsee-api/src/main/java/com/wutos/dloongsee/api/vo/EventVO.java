package com.wutos.dloongsee.api.vo;

import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EventVO {
    private Long id;

    private String carId;

    private String eventId;

    private EventType eventType;

    private String carNum;

    private Integer startMil;

    private Integer endMil;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime disposeTime;

    private Integer disposeStatus;

    private Integer segmentId;

    private RoadDirection direction;

    private Integer wn;

    private Double speed;

    private Integer level;

    private String typeName;

    private String displayMil;

    private String segmentName;
}
