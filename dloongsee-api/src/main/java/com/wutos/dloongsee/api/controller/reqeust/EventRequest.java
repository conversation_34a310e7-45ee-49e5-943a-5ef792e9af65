package com.wutos.dloongsee.api.controller.reqeust;

import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventRequest {
    /**
     * 事件大类：流量失衡、违规驾驶、通行障碍
     */
    private EventCategory category;

    /**
     * 事件类型
     */
    private List<EventType> eventTypes;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private  LocalDate endTime;

    /**
     * 路段 id
     */
    private Integer segmentId;

    private Integer pageSize;

    private Integer pageNum;
}
