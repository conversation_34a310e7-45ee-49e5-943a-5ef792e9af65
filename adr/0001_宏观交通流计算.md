# 宏观交通流计算

**日期**：2025-08-22

## 背景
对高速公路分段进行交通运行状态评估，根据实时轨迹计算每段的**车流量、平均行程速度、平均行程时间、拥堵等级**

## 参数与配置
- 路段划分：每个路段中按照里程从小到大**每 500 米**划分段元，并确保轨迹生成区与消失区在首尾两段。配置表**traffic_capacity_segment**
- 统计周期：**5分钟**，配置文件**application.yml -> traffic-capacity:duration**
- 自由流速度：**120km/h**，配置文件**application.yml -> traffic-capacity:free-speed**
- 拥堵速度阈值：配置文件**application.yml -> traffic-capacity:congestion-level**
  - 1级(严重拥堵)：速度 <= **30km/h**
  - 2级(中度拥堵)：**30km/h** < 速度 <= **50km/h**
  - 3级(轻度拥堵)：**50km/h** < 速度 <= **70km/h**
  - 4级(畅通)：速度 > **70km/h**

## 数据来源
降频后的实时轨迹(1s/帧)

## 计算流程
1. 接收到轨迹, 计算所在路段并缓存
2. 判断是否通过路段：
   - 未结束轨迹：
      1. 如果前后两帧路段不同，判定通过
      2. 如果通过的路段是上高速后的第一段，且不是轨迹生成区所在路段，则为无效样本
   - 已结束轨迹：
      1. 如果从轨迹消失区所在路段结束，判定通过
3. 记录每个路段的通过时间与通过车辆数
4. 到达统计周期，计算结果：
   1. 车流量 = 车辆数
   2. 平均行程时间 = 总时间 / 车辆数
   3. 平均行程速度 = 车辆数 * 路段长度 / 总时间
   4. 拥堵等级判定：车辆数 > (车道数-应急车道数) * 10 为有效路段，根据平均行程速度判定等级